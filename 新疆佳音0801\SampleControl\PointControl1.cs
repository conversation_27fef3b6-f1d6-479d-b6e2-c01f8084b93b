﻿
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace Cellpro



{


    [System.ComponentModel.Designer(typeof(Designer1))]
   // [System.ComponentModel.Designer(typeof(Designer1))]
    public partial class PointControl1 : UserControl
    {
        public Action<int> OnEdit;
        public Action<int> OnShowPic;
        public Action<int> OnPrint;
        public Action<int> OnSelectReportPic;

        private List<int> PointXList = new List<int>();

        private List<int> PointYList = new List<int>();

        public int Rows { get; set; }

        public Color BaseColor { get; set; }

        public Color RedoColor { get; set; }

        public Color UndoColor { get; set; }

        public int Cols { get; set; }


        public PointControl1()
        {
            InitializeComponent();
        }

        public void AddPoint(int x, int y)
        {
            this.PointXList.Add(x);
            this.PointYList.Add(y);
            this.PointPanel.Invalidate();
        }

        public void Clear()
        {
            this.PointXList.Clear();
            this.PointYList.Clear();
            this.PointPanel.Invalidate();
        }


        public static readonly Color RedColor = Color.FromArgb(255, 0, 0);
        public static readonly Color LightBlueColor = Color.FromArgb(235, 243, 255);
        public static readonly Color BlueColor = Color.FromArgb(80, 160, 255);
        public static readonly Color GreenColor = Color.FromArgb(0, 255, 0);
        public static readonly Color OrangeColor = Color.FromArgb(220, 155, 40);
        public static readonly Color GreyColor = Color.FromArgb(111, 111, 111);
        public static readonly Color LightSeaGreen = Color.FromArgb(32, 178, 170);
        public static readonly Color SkyBlue3 = Color.FromArgb(108, 166, 205);



        /// <summary>
        /// 点的宽度
        /// </summary>
        private int PointWidth = 10;

        private void SampleControl_Load(object sender, EventArgs e)
        {
            if (this.BackColor == null) this.BackColor = Color.White;
            if (this.RedoColor == null) this.RedoColor = Color.Black;
            if (this.UndoColor == null) this.UndoColor = Color.Gray;
        }

/*        private void OnPointPaint(object sender, PaintEventArgs e)
        {
            UIPanel uIPanel = sender as UIPanel;

        }*/


 

        private void SampleControl_Resize(object sender, EventArgs e)
        {
            this.Width = 170;
            this.Height = 170;
/*            this.PointPanel.Width = 190;
            this.PointPanel.Height = 190;*/
        }

        private void PointPanel_Paint(object sender, PaintEventArgs e)
        {
    
            Graphics graphics = e.Graphics;
            Pen LineColor = new Pen(Color.Gray, 5);
            int width = this.PointPanel.Size.Width;
            int height = this.PointPanel.Size.Height;
            double step_x = (width - this.Cols * this.PointWidth) / (this.Cols + 1);
            double step_y = (height - this.Rows * this.PointWidth) / (this.Rows + 1);
            for (int i = 0; i < Cols; i++)
            {
                for (int j = 0; j < Rows; j++)
                {
                    graphics.DrawPoint(this.UndoColor, (int)(step_x * (i + 1)), (int)(step_x * (j + 1)), 10);
                }
            }
            if (this.PointXList == null || this.PointXList.Count == 0) return;
            for (int i = 0; i < this.PointXList.Count; i ++)
            {
                int x = (int)(step_x * this.PointYList[i]);
                int y = (int)(step_x * this.PointYList[i]);
                graphics.DrawPoint(this.UndoColor, x, y, 10);
                if (i == 0) continue;
                graphics.DrawLine(LineColor, (float)(step_x * this.PointYList[i - 1]), (float)(step_x * this.PointYList[i - 1]), x, y);
            }
            LineColor.Dispose();
            graphics.Dispose();
            /*ControlPaint.DrawBorder(e.Graphics, uiPanel1.ClientRectangle,
                Color.White, 1, ButtonBorderStyle.Solid, //左边
　　　　　        this.Index == 0 ? Color.DimGray : Color.White, 1, ButtonBorderStyle.Solid, //上边
　　　　　        Color.White, 1, ButtonBorderStyle.Solid, //右边
　　　　　        Color.DimGray, 1, ButtonBorderStyle.Solid);//底边*/

            /*this.PointPanel.RectColor = Color.Transparent;
            int size;
            if (this.PointStatus == null || (size = this.PointStatus.Length) == 0) return;

            for (int i = 0; i < size; i ++)
            {
                ((UIPanel)(this.PointPanel.Controls[i])).FillColor = this.PointStatus[i] == 0 ? GreyColor : SkyBlue3;
                ((UIPanel)(this.PointPanel.Controls[i])).RectColor = Color.Transparent;
            }*/
        }

        private void uiLabel7_Click(object sender, EventArgs e)
        {

        }

        private void uiPanel1_Click(object sender, EventArgs e)
        {

        }

        private void PointPanel_Click(object sender, EventArgs e)
        {

        }

        private void PointPanel_Load(object sender, EventArgs e)
        {
            
            /*if (this.Rows * this.Cols == 0) return;
            this.PointStatus = new int[Rows * Cols];
            int width = this.PointPanel.Size.Width;
            int height = this.PointPanel.Size.Height;
            height = width;
            double padding_x = (width - this.Cols * this.PointWidth) / (this.Cols + 1);
            double padding_y = (height - this.Rows * this.PointWidth) / (this.Rows + 1);
            for (int i = 0; i < Cols; i++)
            {
                for (int j = 0; j < Rows; j++)
                {
                    this.PointStatus[i + j * this.Cols] = 0;
                    UIPanel p = new UIPanel
                    {
                        Size = new Size(this.PointWidth, this.PointWidth),
                        Radius = this.PointWidth,
                        Margin = new Padding(0, 0, 0, 0),
                        FillColor = GreyColor,
                        RectColor = Color.Transparent,
                    Location = new Point((int)(padding_x + i * (padding_x + this.PointWidth)), (int)(padding_y + j * (padding_y + this.PointWidth)))
                    };

                    this.PointPanel.Controls.Add(p);

                }
            }*/
        }
    }




}

