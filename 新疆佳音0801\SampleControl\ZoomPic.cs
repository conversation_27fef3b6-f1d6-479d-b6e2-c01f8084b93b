﻿
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace Cellpro
{
    [System.ComponentModel.Designer(typeof(Designer1))]
    public partial class ZoomPic : UserControl
    {
        public Action<int> OnEnter;

        public Action<int, Image> OnClick;

        public Action<int> OnMouseLeave;

        public Action<int> OnDoubleClick;

        // public int status { get; set; }

        public int Index { get; set; }

        public Image Image
        {
            get { return this.pictureBox1.Image; }
            set { this.pictureBox1.Image = value; }
        }

        public override Color BackColor
        {
            get { return this.uiPanel1.BackColor; }
            set { this.uiPanel1.BackColor = value; }
        }

        public string Text
        {
            get { return this.uiLabel1.Text; }
            set { this.uiLabel1.Text = value; }
        }

        public ZoomPic()
        {
            InitializeComponent();
        }


        public static readonly Color RedColor = Color.FromArgb(255, 0, 0);
        public static readonly Color LightBlueColor = Color.FromArgb(235, 243, 255);
        public static readonly Color BlueColor = Color.FromArgb(80, 160, 255);
        public static readonly Color GreenColor = Color.FromArgb(0, 255, 0);
        public static readonly Color OrangeColor = Color.FromArgb(220, 155, 40);

 

        private void SampleControl_Load(object sender, EventArgs e)
        {
            this.uiPanel1.BackColor = Color.White;
            this.pictureBox1.Location = new System.Drawing.Point(13, 26);
            this.pictureBox1.Size = new System.Drawing.Size(144, 90);
            this.uiLabel1.Location = new System.Drawing.Point(13, 123);
            this.uiLabel1.Size = new System.Drawing.Size(144, 23);
        }

       /* public void MouseLeave(int index)
        {
            if (this.Index == index || this.status == 1) return;
            this.uiPanel1.BackColor = Color.White;
        }*/

        public void MouseLeave()
        {
            OnMouseLeave(Index);
        }




        private void SampleControl_Resize(object sender, EventArgs e)
        {
            this.Width = 170;
            this.Height = 170;
        
        }

        private void uiPanel1_MouseEnter(object sender, EventArgs e)
        {
            /*if (this.OnEnter != null) */this.OnEnter(this.Index);
/*            if (this.status == 1 || this.Image == null) return;
            this.uiPanel1.BackColor = Color.LightBlue;*/
            
        }

        private void uiPanel1_MouseLeave(object sender, EventArgs e)
        {
            OnMouseLeave(Index);
           /* if (this.status == 1) return;
            this.uiPanel1.BackColor = Color.White;*/
        }

        private void uiPanel1_Click(object sender, EventArgs e)
        {
             OnClick(this.Index, this.Image);
        }

        private void uiPanel1_DoubleClick(object sender, EventArgs e)
        {
            OnDoubleClick(this.Index);
        }
    }
}

