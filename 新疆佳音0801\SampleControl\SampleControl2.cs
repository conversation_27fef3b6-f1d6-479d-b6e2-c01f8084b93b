﻿
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace Cellpro



{


    //[System.ComponentModel.Designer(typeof(Designer1))]
    [System.ComponentModel.Designer(typeof(Designer1))]
    public partial class SampleControl2 : UserControl
    {
        public Action<int> OnEdit;
        public Action<int> OnShowPic;
        public Action<int> OnPrint;
        public Action<int> OnSelectReportPic;

        public int Rows { get; set; }

        public int Cols { get; set; }

        private int[] PointStatus;

        
        public int Index { get; set; }
        public SampleControl2()
        {
            InitializeComponent();
            this.uiPanel1.RectColor = Color.DimGray;
            this.uiToolTip1.SetToolTip(uiSymbolButton4, "选择报告图");
            this.uiToolTip1.SetToolTip(uiSymbolButton2, "打印报告");
            this.uiToolTip1.SetToolTip(uiSymbolButton1, "编辑样本信息");
            this.uiToolTip1.SetToolTip(uiSymbolButton3, "查看图片");
        }

        /// <summary>
        /// 查看图片
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton3_Click(object sender, EventArgs e)
        {
            if (this.OnShowPic != null)
                this.OnShowPic(this.Index);
        }

        public static readonly Color RedColor = Color.FromArgb(255, 0, 0);
        public static readonly Color LightBlueColor = Color.FromArgb(235, 243, 255);
        public static readonly Color BlueColor = Color.FromArgb(80, 160, 255);
        public static readonly Color GreenColor = Color.FromArgb(0, 255, 0);
        public static readonly Color OrangeColor = Color.FromArgb(220, 155, 40);
        public static readonly Color GreyColor = Color.FromArgb(111, 111, 111);
        public static readonly Color LightSeaGreen = Color.FromArgb(32, 178, 170);
        public static readonly Color SkyBlue3 = Color.FromArgb(108, 166, 205);


        public void SetData(int status, string Patient_ID, string Test_ID, int RightCellNum, int ErrorCellNum, int TotalNum
            , double DFI_Value, string Patient_Name, string Patient_Age, string Sample_type, string Send_Doctor, string Tester, string Send_date, string Test_date
            , string reviewer, string remark)
        {

            if (status == 0)
            {
                this.uiToolTip1.RemoveToolTip(this.Label);
                this.Patient_No.Text = "";
                this.Test_No.Text = "";
                this.RightNum.Text = "";
                this.ErrNum.Text = "";
                this.TotalNum.Text = "";
                this.DFIVal.Text = "";
                this.uiPanel1.RectColor = Color.DimGray;
            }
            else if (status == 1)
            {
                this.uiToolTip1.RemoveToolTip(this.Label);
                this.Patient_No.Text = Patient_ID;
                this.Test_No.Text = Test_ID;
                this.RightNum.Text = "";
                this.ErrNum.Text = "";
                this.TotalNum.Text = "";
                this.DFIVal.Text = "";
                this.uiPanel1.RectColor = BlueColor;
                this.uiToolTip1.SetToolTip(this.Label, "姓名：" + Patient_Name + "\n年龄：" + Patient_Age + "\n样本类型：" + Sample_type
                    + "\n送检医生" + Send_Doctor + "\n检测人：" + Tester + "\n送检日期：" + Send_date + "\n检测日期：" + Test_date
                    + "\n复核人：" + reviewer + "\n备注" + remark);
            }
            else if (status == 2)
            {
                this.Patient_No.Text = Patient_ID;
                this.Test_No.Text = Test_ID;
                this.RightNum.Text = RightCellNum.ToString();
                this.ErrNum.Text = ErrorCellNum.ToString();
                this.TotalNum.Text = TotalNum.ToString();
                this.DFIVal.Text = DFI_Value.ToString() + "%";
                this.uiPanel1.RectColor = GreenColor;
            }
            if (status >= 1)
            {

            }
        }


        /// <summary>
        /// 点的宽度
        /// </summary>
        private int PointWidth = 8;

        private void SampleControl_Load(object sender, EventArgs e)
        {

            this.Label.Text = "#" + (this.Index + 1);
            if (this.Rows * this.Cols == 0) return;
            this.PointStatus = new int[Rows * Cols];
            int width = this.PointPanel.Size.Width;
            int height = this.PointPanel.Size.Height;
            double padding_x = (width - this.Cols * this.PointWidth) / (this.Cols + 1);
            double padding_y = (height - this.Rows * this.PointWidth) / (this.Rows + 1);
            for (int i = 0; i < Cols; i++)
            {
                for (int j = 0; j < Rows; j++)
                {
                    this.PointStatus[i + j * this.Cols] = 0;
                    UIPanel p = new UIPanel
                    {
                        Size = new Size(this.PointWidth, this.PointWidth),
                        Radius = this.PointWidth,
                        Margin = new Padding(0, 0, 0, 0),
                        FillColor = RedColor,
                        Location = new Point((int)(padding_x + i * (padding_x + this.PointWidth)), (int)(padding_y + j * (padding_y + this.PointWidth)))
                    };
                    
                    this.PointPanel.Controls.Add(p);
                    
                }
            }
        }

/*        private void OnPointPaint(object sender, PaintEventArgs e)
        {
            UIPanel uIPanel = sender as UIPanel;

        }*/

        /// <summary>
        /// 选择报告图
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton4_Click(object sender, EventArgs e)
        {
            if (this.OnSelectReportPic != null) this.OnSelectReportPic(this.Index);
        }

        /// <summary>
        /// 打印报告
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton2_Click(object sender, EventArgs e)
        {
            if (this.OnPrint != null)
                this.OnPrint(this.Index);
        }

        /// <summary>
        /// 编辑
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton1_Click(object sender, EventArgs e)
        {
            if (this.OnEdit != null) OnEdit(this.Index);
        }

        private void SampleControl_Resize(object sender, EventArgs e)
        {
            this.Width = 444;
            this.Height = 126;
        }

        private void PointPanel_Paint(object sender, PaintEventArgs e)
        {

            this.PointPanel.RectColor = Color.Transparent;
            int size;
            if (this.PointStatus == null || (size = this.PointStatus.Length) == 0) return;
            for (int i = 0; i < size; i ++)
            {
                ((UIPanel)(this.PointPanel.Controls[i])).FillColor = this.PointStatus[i] == 0 ? GreyColor : SkyBlue3;
                ((UIPanel)(this.PointPanel.Controls[i])).RectColor = Color.Transparent;
            }
        }

        private void uiLabel7_Click(object sender, EventArgs e)
        {

        }
    }




}

