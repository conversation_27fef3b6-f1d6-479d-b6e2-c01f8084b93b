﻿
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace Cellpro



{


    //[System.ComponentModel.Designer(typeof(Designer1))]
    [System.ComponentModel.Designer(typeof(Designer1))]
    public partial class SampleControl4 : UserControl
    {
        public Action<int> OnEdit;
        public Action<int> OnShowPic;
        public Action<int> OnPrint;
        public Action<int> OnSelectReportPic;

        public int Rows { get; set; }

        public int Cols { get; set; }

        private int[] PointStatus;

        
        public int Index { get; set; }
        public SampleControl4()
        {
            InitializeComponent();
            this.Label.Text = "#" + (this.Index + 1);
            /*this.uiPanel1.RectColor = Color.DimGray;
            this.uiPanel1.RectSides = ToolStripStatusLabelBorderSides.Bottom;*/
            this.uiToolTip1.SetToolTip(uiSymbolButton4, "选择报告图");
            this.uiToolTip1.SetToolTip(uiSymbolButton2, "打印报告");
            this.uiToolTip1.SetToolTip(uiSymbolButton1, "编辑样本信息");
            this.uiToolTip1.SetToolTip(uiSymbolButton3, "查看图片");
        }

        /// <summary>
        /// 查看图片
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton3_Click(object sender, EventArgs e)
        {
            if (this.OnShowPic != null)
                this.OnShowPic(this.Index);
        }

        public static readonly Color RedColor = Color.FromArgb(255, 0, 0);
        public static readonly Color LightBlueColor = Color.FromArgb(235, 243, 255);
        public static readonly Color BlueColor = Color.FromArgb(80, 160, 255);
        public static readonly Color GreenColor = Color.FromArgb(0, 255, 0);
        public static readonly Color OrangeColor = Color.FromArgb(220, 155, 40);
        public static readonly Color GreyColor = Color.FromArgb(111, 111, 111);
        public static readonly Color LightSeaGreen = Color.FromArgb(32, 178, 170);
        public static readonly Color SkyBlue3 = Color.FromArgb(108, 166, 205);

        public void SetDate(int Num, int err, string DFi)
        {

        }

        /// <summary>
        /// 样本状态
        /// 0 ： 未完善信息
        /// 1 ： 已填写信息
        /// 2 ： 已核对（可拍摄）
        /// 3：  拍摄中
        /// 4：  已拍摄
        /// 5：  已计算
        /// </summary>
        /// <param name="status"></param>
        /// <param name="Patient_ID"></param>
        /// <param name="Test_ID"></param>
        /// <param name="RightCellNum"></param>
        /// <param name="ErrorCellNum"></param>
        /// <param name="TotalNum"></param>
        /// <param name="DFI_Value"></param>
        /// <param name="Patient_Name"></param>
        /// <param name="Patient_Age"></param>
        /// <param name="Sample_type"></param>
        /// <param name="Send_Doctor"></param>
        /// <param name="Tester"></param>
        /// <param name="Send_date"></param>
        /// <param name="Test_date"></param>
        /// <param name="reviewer"></param>
        /// <param name="remark"></param>
        public void SetData(int status, string Patient_ID, string Test_ID, int RightCellNum, int ErrorCellNum, int TotalNum
            , double DFI_Value, string Patient_Name, string Patient_Age, string Sample_type, string Send_Doctor, string Tester, string Send_date, string Test_date
            , string reviewer, string remark)
        {

            if (status == 0)
            {
                this.uiToolTip1.RemoveToolTip(this.Label);
                this.uiToolTip1.RemoveToolTip(this.PatienIdLabel);
                this.uiToolTip1.RemoveToolTip(this.Patient_No);
                this.Patient_No.Text = "";
                this.ErrNum.Text = "";
                this.TotalNum.Text = "";
                this.DFIVal.Text = "";
               // this.uiPanel1.RectColor = Color.DimGray;
            }
            else if (status <= 3)
            {
                this.uiToolTip1.RemoveToolTip(this.Label);
                this.uiToolTip1.RemoveToolTip(this.PatienIdLabel);
                this.uiToolTip1.RemoveToolTip(this.Patient_No);
                this.Patient_No.Text = Patient_ID;
                this.ErrNum.Text = "";
                this.TotalNum.Text = "";
                this.DFIVal.Text = "";
                //this.uiPanel1.RectColor = BlueColor;
                string info = "门诊编号：" + Patient_ID + "\n检测编号：" + Test_ID + "\n姓名：" + Patient_Name + "\n年龄：" + Patient_Age + "\n样本类型：" + Sample_type
                    + "\n送检医生" + Send_Doctor + "\n检测人：" + Tester + "\n送检日期：" + Send_date + "\n检测日期：" + Test_date
                    + "\n复核人：" + reviewer + "\n备注" + remark;
                this.uiToolTip1.SetToolTip(this.Label, info, "#" + (this.Index) + "号样本信息");
                this.uiToolTip1.SetToolTip(this.PatienIdLabel, info, "#" + (this.Index) + "号样本信息");
                this.uiToolTip1.SetToolTip(this.Patient_No, info, "#" + (this.Index) + "号样本信息");
            }
            else if (status == 4)
            {
                this.Patient_No.Text = Patient_ID;
                this.TotalNum.Text = TotalNum.ToString();
                //this.uiPanel1.RectColor = GreenColor;
            }
            else if (status == 5)
            {
                this.Patient_No.Text = Patient_ID;
                this.ErrNum.Text = ErrorCellNum.ToString();
                this.TotalNum.Text = TotalNum.ToString();
                this.DFIVal.Text = DFI_Value.ToString() + "%";
            }
        }

        public void SetName(string name)
        {
            PatienIdLabel.Text = name;
        }

        private void SampleControl_Load(object sender, EventArgs e)
        {



            // 

            this.uiSymbolButton4.FillColor = System.Drawing.Color.White;
            this.uiSymbolButton4.ForeColor = System.Drawing.Color.DimGray;
            this.uiSymbolButton4.Location = new System.Drawing.Point(383, 34);
            this.uiSymbolButton4.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiSymbolButton4.RectColor = System.Drawing.Color.White;
            this.uiSymbolButton4.Size = new System.Drawing.Size(30, 24);
            this.uiSymbolButton4.Style = Sunny.UI.UIStyle.Custom;
            this.uiSymbolButton4.Symbol = 57559;
            this.uiSymbolButton4.TabIndex = 16;
            this.uiSymbolButton4.TipsFont = new System.Drawing.Font("Microsoft Sans Serif", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            // 
            // DFIVal
            // 
            this.DFIVal.Font = new System.Drawing.Font("楷体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DFIVal.Location = new System.Drawing.Point(290, 34);
            this.DFIVal.Size = new System.Drawing.Size(56, 19);
            this.DFIVal.TabIndex = 15;
            this.DFIVal.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // TotalNum
            // 
            this.TotalNum.Font = new System.Drawing.Font("楷体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TotalNum.Location = new System.Drawing.Point(305, 4);
            this.TotalNum.Size = new System.Drawing.Size(81, 19);
            this.TotalNum.TabIndex = 14;
            this.TotalNum.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // ErrNum
            // 
            this.ErrNum.Font = new System.Drawing.Font("楷体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ErrNum.Location = new System.Drawing.Point(124, 34);
            this.ErrNum.Size = new System.Drawing.Size(74, 19);
            this.ErrNum.TabIndex = 13;
            this.ErrNum.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // Patient_No
            // 
            this.Patient_No.Font = new System.Drawing.Font("楷体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Patient_No.Location = new System.Drawing.Point(124, 4);
            this.Patient_No.Size = new System.Drawing.Size(74, 19);
            this.Patient_No.TabIndex = 10;
            this.Patient_No.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel7
            // 
            this.uiLabel7.Font = new System.Drawing.Font("楷体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel7.Location = new System.Drawing.Point(229, 34);
            this.uiLabel7.Size = new System.Drawing.Size(42, 19);
            this.uiLabel7.TabIndex = 9;
            this.uiLabel7.Text = "DFI:";
            this.uiLabel7.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uiLabel6
            // 
            this.uiLabel6.Font = new System.Drawing.Font("楷体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel6.Location = new System.Drawing.Point(56, 34);
            this.uiLabel6.Size = new System.Drawing.Size(80, 19);
            this.uiLabel6.TabIndex = 8;
            this.uiLabel6.Text = "异常细胞：";
            this.uiLabel6.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel5
            // 
            this.uiLabel5.Font = new System.Drawing.Font("楷体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel5.Location = new System.Drawing.Point(229, 4);
            this.uiLabel5.Size = new System.Drawing.Size(80, 19);
            this.uiLabel5.TabIndex = 7;
            this.uiLabel5.Text = "细胞总数:";
            this.uiLabel5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel2
            // 
            this.PatienIdLabel.Font = new System.Drawing.Font("楷体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.PatienIdLabel.Location = new System.Drawing.Point(56, 4);
            this.PatienIdLabel.Size = new System.Drawing.Size(77, 19);
            this.PatienIdLabel.TabIndex = 4;
            this.PatienIdLabel.Text = "门诊编号：";
            this.PatienIdLabel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiSymbolButton3
            // 
            this.uiSymbolButton3.FillColor = System.Drawing.Color.White;
            this.uiSymbolButton3.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiSymbolButton3.ForeColor = System.Drawing.Color.DimGray;
            this.uiSymbolButton3.Location = new System.Drawing.Point(356, 34);
            this.uiSymbolButton3.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiSymbolButton3.RectColor = System.Drawing.Color.White;
            this.uiSymbolButton3.Size = new System.Drawing.Size(30, 24);
            this.uiSymbolButton3.Style = Sunny.UI.UIStyle.Custom;
            this.uiSymbolButton3.Symbol = 61893;
            this.uiSymbolButton3.TabIndex = 3;
            // 
            // uiSymbolButton2
            // 
            this.uiSymbolButton2.FillColor = System.Drawing.Color.White;
            this.uiSymbolButton2.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiSymbolButton2.ForeColor = System.Drawing.Color.DimGray;
            this.uiSymbolButton2.Location = new System.Drawing.Point(410, 34);
            this.uiSymbolButton2.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiSymbolButton2.RectColor = System.Drawing.Color.White;
            this.uiSymbolButton2.Size = new System.Drawing.Size(30, 24);
            this.uiSymbolButton2.Style = Sunny.UI.UIStyle.Custom;
            this.uiSymbolButton2.Symbol = 57594;
            this.uiSymbolButton2.TabIndex = 2;
            this.uiSymbolButton2.Click += new System.EventHandler(this.uiSymbolButton2_Click);
            // 
            // uiSymbolButton1
            // 
            this.uiSymbolButton1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.uiSymbolButton1.FillColor = System.Drawing.Color.White;
            this.uiSymbolButton1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiSymbolButton1.ForeColor = System.Drawing.Color.DimGray;
            this.uiSymbolButton1.Location = new System.Drawing.Point(437, 34);
            this.uiSymbolButton1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiSymbolButton1.RectColor = System.Drawing.Color.White;
            this.uiSymbolButton1.Size = new System.Drawing.Size(30, 24);
            this.uiSymbolButton1.Style = Sunny.UI.UIStyle.Custom;
            this.uiSymbolButton1.Symbol = 57434;
            this.uiSymbolButton1.TabIndex = 1;
            // 
            // Label
            // 
            this.Label.Font = new System.Drawing.Font("微软雅黑", 15.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Label.Location = new System.Drawing.Point(4, 4);
            this.Label.Size = new System.Drawing.Size(49, 54);
            this.Label.TabIndex = 0;
            this.Label.Text = "#" + (this.Index + 1);
            this.Label.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;

        }

/*        private void OnPointPaint(object sender, PaintEventArgs e)
        {
            UIPanel uIPanel = sender as UIPanel;

        }*/

        /// <summary>
        /// 选择报告图
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton4_Click(object sender, EventArgs e)
        {
           
            if (this.OnSelectReportPic != null) this.OnSelectReportPic(this.Index);
        }

        /// <summary>
        /// 打印报告
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton2_Click(object sender, EventArgs e)
        {
            if (this.OnPrint != null)
                this.OnPrint(this.Index);
        }

        /// <summary>
        /// 编辑
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton1_Click(object sender, EventArgs e)
        {
            if (this.OnEdit != null) OnEdit(this.Index);
        }

        private void SampleControl_Resize(object sender, EventArgs e)
        {
            this.Width = 470;
            this.Height = 65;
        }

        private void uiPanel1_Paint(object sender, PaintEventArgs e)
        {
            ControlPaint.DrawBorder(e.Graphics, uiPanel1.ClientRectangle,
                Color.White, 1, ButtonBorderStyle.Solid, //左边
　　　　　        this.Index == 0 ? Color.DimGray : Color.White, 1, ButtonBorderStyle.Solid, //上边
　　　　　        Color.White, 1, ButtonBorderStyle.Solid, //右边
　　　　　        Color.DimGray, 1, ButtonBorderStyle.Solid);//底边
        }
    }




}

