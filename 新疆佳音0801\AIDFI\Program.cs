﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using AIDFI.Class;
using AIDFI.Data;
using AIDFI.Form;
using AIDFI.test;
using AIDFI.utils;
using Newtonsoft.Json.Linq;
using NPOI.Util;
using System.Windows.Forms;

namespace AIDFI
{


    static class Program 
    {

        static DBUtils dBUtils = new DBUtils();//数据库操作类
        static string getMd5Hash(string input)
        {

            MD5CryptoServiceProvider md5Hasher = new MD5CryptoServiceProvider();
            byte[] data = md5Hasher.ComputeHash(Encoding.Default.GetBytes(input));
            StringBuilder sBuilder = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sBuilder.Append(data[i].ToString("x2"));
            }
            return sBuilder.ToString();
        }

        public static string KeyPath;
        //static SoftKeyYT88 ytsoftkey;
        static SoftKeyYT88 ytsoftkey = new SoftKeyYT88();
        private static readonly log4net.ILog loginfo = log4net.LogManager.GetLogger("loginfo");

        public static bool check()
        {
            string cpuID = clsUtility.GetCpuID();
            //检验ID
            int id_11 = 0, id_21 = 0;
            if (ytsoftkey.GetID(ref id_11, ref id_21, KeyPath) != 0) { return false; }
            string res1 = id_11.ToString("X8") + id_21.ToString("X8");
            if (!GlobalProperty.Id.Equals(res1))
            {
                //MessageBox.Show("ID不对");
                return false;
            }
            if (!cpuID.Equals(GlobalProperty.Machine_Code))
            {
                //MessageBox.Show("机器码不对——"+ cpuID+"---"+ GlobalProperty.Machine_Code);
                return false;
            }
            return true;
        }


        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                log4net.Config.XmlConfigurator.Configure(new FileInfo(AppDomain.CurrentDomain.BaseDirectory + "//log4net.config"));
                GlobalProperty.InitParams();

                //这个用于判断系统中是否存在着加密锁。不需要是指定的加密锁,
                //if ((ytsoftkey.FindPort(0, ref KeyPath) != 0 && GlobalProperty.Id != "init"))
                //{
                //    MessageBox.Show("未检测到加密U盘", "浙江星博版权所有");
                //    Application.Exit();
                //    return;
                //}

                //if (!check())
                //{
                //    MessageBox.Show("非法软件使用", "浙江星博版权所有");
                //    return;
                //}

                _ = new Mutex(true, "AIDFI", out bool isCreated);
                if (!isCreated)//如果已启动一个FCMCAS实例
                {
                    MessageBox.Show("您已经启动了一个精子分析管理软件 ，此软件不能同时启动多个！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    Application.Exit();
                    return;
                }
                else
                {
                    //检测数据库
                    string str = DateTime.Now.ToString("yyyyMMdd").Substring(0, 6);
                    //string sql = "SELECT* FROM information_schema.TABLES where table_name = 'dfi_details" + str+"' and TABLE_SCHEMA = 'cellpro'";
                    string sql = "SELECT* FROM sqlite_master where name = 'dfi_details" + str + "'";
                    DataTable dt = dBUtils.GetDataTable(sql, "test");
                    if (dt.Rows.Count == 0)
                    {


                        string sqlcreatetable = "DROP TABLE IF EXISTS `dfi_details" + str + "`;\n" +
                                                "CREATE TABLE `dfi_details" + str + "`  (\n" +
                                                "  `id` integer PRIMARY KEY AUTOINCREMENT,\n" +
                                                "  `p_id` text NULL DEFAULT NULL ,\n" +
                                                "  `x` int NULL DEFAULT NULL,\n" +
                                                "  `y` int NULL DEFAULT NULL,\n" +
                                                "  `width` int NULL DEFAULT NULL,\n" +
                                                "  `height` int NULL DEFAULT NULL,\n" +
                                                "  `color` int NULL DEFAULT NULL\n" +
                                                ");\n" +
                                                "CREATE INDEX IND_p_id" + str + " ON dfi_details" + str + "(p_id);";
                        int res = dBUtils.ExecDataBySql(sqlcreatetable);
                    }

                }

                log4net.Config.XmlConfigurator.Configure(new FileInfo(AppDomain.CurrentDomain.BaseDirectory + "//log4net.config"));
                GlobalProperty.InitParams();


                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                if (GlobalProperty.CheckCommunication)
                //if (false)
                {
                    Splasher.Show(typeof(SplashView));
                    //Application.Run(new Login());
                    Login login11 = new Login
                    {
                        ShowInTaskbar = true,
                        TopMost = true
                    };
                    //Main instance1 = new Main();
                    //Application.Run(instance1);
                    if (login11.ShowDialog() == DialogResult.OK)
                    {
                        Main instance1 = new Main();
                        //Application.SetCompatibleTextRenderingDefault(false);
                        //AccessViolationException:“尝试读取或写入受保护的内存。这通常指示其他内存已损坏。”
                        Application.Run(instance1);
                    }
                }
                else
                {
                    Login login1 = new Login
                    {
                        ShowInTaskbar = true,
                        TopMost = true
                    };
                    if (login1.ShowDialog() == DialogResult.OK)
                    {
                        Main instance = new Main();
                        Application.EnableVisualStyles();
                        //Application.SetCompatibleTextRenderingDefault(false);
                        Application.EnableVisualStyles();
                        Application.Run(instance);
                    }
                }

            }
            catch (Exception ex)
            {
                LogHelper.WriteLog("窗体初始化异常",ex);
            }       
        }
    }
}
    