using AIDFI.Class;
using AIDFI.Data;
using AIDFI.Entity;
//using AIDFI.Form.Snap;
using AIDFI.Properties;
//using AIDFI.Snap;
using AIDFI.utils;
using Cellpro;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.UserModel;
using NPOI.POIFS.FileSystem;
using NPOI.SS.UserModel;
using Sunny.UI;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SQLite;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace AIDFI.Form.Snap
{
    public partial class AutoSnapView : UITitlePage
    {

        
/*      public static readonly Color RedColor = Color.FromArgb(255,0,0);
        public static readonly Color LightBlueColor = Color.FromArgb(235, 243, 255);
        public static readonly Color BlueColor = Color.FromArgb(80, 160, 255);
        public static readonly Color GreenColor = Color.FromArgb(0, 255, 0);
        public static readonly Color OrangeColor = Color.FromArgb(220, 155, 40);*/
        private readonly DataLogic_Access dal = new DataLogic_Access();//数据库操作类

        private static readonly log4net.ILog Log = log4net.LogManager.GetLogger("loginfo");

        private readonly AddSample addSample = new AddSample();

        public static List<PictureInfo> pictureInfos = new List<PictureInfo>();

        /// <summary>
        /// 初始化结果
        /// </summary>
        private volatile int InitResult = -100;


        /// <summary>
        /// 所有点位数
        /// </summary>
        private int SnapPointSize;

        /// <summary>
        /// 已拍摄点位数
        /// </summary>
        private int SnapedPointSize;


        /// <summary>x
        /// 设备状态
        /// 0 未准备
        /// 1 准备好
        /// 2 正在进行
        /// 3 拍摄完成
        /// </summary>
        int DeviceStatus;

        public int GetDeviceStatus()
        {
            return this.DeviceStatus;
        }
        
        private readonly SampleControl4[] SampleControls = new SampleControl4[5];
        private readonly ZoomPic[] ZoomPics = new ZoomPic[8];
        private Snap.EquipAdapter Adaptor = Snap.EquipAdapter.GetInstance();
        SynchronizationContext m_SyncContext;
        private IntPtr m_pBitmapInfo;


        /// <summary>
        /// 线程安全消息队列
        /// </summary>
        private ConcurrentQueue<byte[]> MsgQueue = new ConcurrentQueue<byte[]>();
        private byte[] MsgBuffer;

        static CheckNum.CellParams _cellParams = new CheckNum.CellParams();
        static CheckNum.ColorParams _colorParams = new CheckNum.ColorParams();

        public AutoSnapView()
        {
           
            InitializeComponent();

            #region 初始化数组
            this.SampleControls[0] = this.sampleControl41;
            this.SampleControls[1] = this.sampleControl42;
            this.SampleControls[2] = this.sampleControl43;
            this.SampleControls[3] = this.sampleControl44;
            this.SampleControls[4] = this.sampleControl45;

            this.ZoomPics[0] = this.zoomPic1;
            this.ZoomPics[1] = this.zoomPic2;
            this.ZoomPics[2] = this.zoomPic3;
            this.ZoomPics[3] = this.zoomPic4;
            this.ZoomPics[4] = this.zoomPic5;
            this.ZoomPics[5] = this.zoomPic6;
            this.ZoomPics[6] = this.zoomPic7;
            this.ZoomPics[7] = this.zoomPic8;

            this.ControlPanel.FillColor = Color.White;
            this.ControlPanel.RectColor = Color.White;
            this.MainPicPanel.FillColor = Color.White;
            this.MainPicPanel.RectColor = Color.White;
            this.PicSetPanel.FillColor = Color.White;
            this.PicSetPanel.RectColor = Color.White;
            this.uiSymbolButton1.FillColor = Color.White;
            this.uiSymbolButton1.RectColor = Color.White;
            this.uiSymbolButton1.ForeColor = Color.Gray;
            this.uiSymbolButton2.FillColor = Color.White;
            this.uiSymbolButton2.RectColor = Color.White;
            this.uiSymbolButton2.ForeColor = Color.Gray;
            /*this.uiSymbolButton3.FillColor = Color.White;
            this.uiSymbolButton3.RectColor = Color.White;
            this.uiSymbolButton3.ForeColor = _steelColor;*/
            this.PicSetPanel.FillColor = Color.White;
            this.PicSetPanel.RectColor = Color.White;


            this.PrePicBtn.FillColor = System.Drawing.Color.White;
            this.PrePicBtn.ForeColor = System.Drawing.Color.Gray;
            this.PrePicBtn.RectColor = System.Drawing.Color.White;
            this.NextPicBtn.RectColor = System.Drawing.Color.White;
            this.NextPicBtn.FillColor = System.Drawing.Color.White;
            this.NextPicBtn.ForeColor = System.Drawing.Color.Gray;
            this.MainPicStatus = 0;

            for (int i = 0; i < this.ZoomPics.Length; i++)
            {
                this.ZoomPics[i].OnEnter += this.OnZoomPicEnter;
                this.ZoomPics[i].OnClick += this.OnZoomPicClick;
                this.ZoomPics[i].OnMouseLeave += this.OnZoomPicMouseLeave;
            }
            this.ResetZoomPic();

            CWin32Bitmaps.BITMAPINFO m_objBitmapInfo = new CWin32Bitmaps.BITMAPINFO();
            m_objBitmapInfo.bmiHeader.biSize = (uint)Marshal.SizeOf(typeof(CWin32Bitmaps.BITMAPINFOHEADER));
            m_objBitmapInfo.bmiHeader.biWidth = 1920;
            m_objBitmapInfo.bmiHeader.biHeight = -1200;
            m_objBitmapInfo.bmiHeader.biPlanes = 1;
            m_objBitmapInfo.bmiHeader.biBitCount = 24;
            m_objBitmapInfo.bmiHeader.biCompression = 0;
            m_objBitmapInfo.bmiHeader.biSizeImage = 1920 * 1200 * 3;
            m_objBitmapInfo.bmiHeader.biXPelsPerMeter = 0;
            m_objBitmapInfo.bmiHeader.biYPelsPerMeter = 0;
            m_objBitmapInfo.bmiHeader.biClrUsed = 0;
            m_objBitmapInfo.bmiHeader.biClrImportant = 0;
            this.m_pBitmapInfo = Marshal.AllocHGlobal(2048);
            Marshal.StructureToPtr(m_objBitmapInfo, this.m_pBitmapInfo, false);
            #endregion

            for (int i = 0; i < 5; i ++)
            {
                this.SampleControls[i].OnEdit = this.EditSample;
                this.SampleControls[i].OnPrint = this.Print;
                this.SampleControls[i].OnShowPic = this.OnShowPicZoom;
                this.SampleControls[i].OnSelectReportPic = this.OnSelectReportPic;
            }
            this.m_SyncContext = SynchronizationContext.Current;
            this.addSample.OnChangeData += this.FlushSamplePanelData;
            //this.Adaptor = DeviceAdaptor.GetAdaptor();
            this.Adaptor.OnAutoSnap += this.OnSnap;
             // this.Adaptor.OnGetAutoStatus += () => { return }
            SQLiteDataReader sdr = null;
            string strSql = "select * from param";
            try
            {
                sdr = dal.GetDataReader(strSql);
                if (sdr.HasRows)  //若该用户编码无数据记录
                {
                    sdr.Read(); //读取唯一的一行记录
                    _colorParams.greenMin = Convert.ToInt32(sdr["green1"].ToString());
                    _colorParams.greenMax = Convert.ToInt32(sdr["green2"].ToString());
                    _colorParams.yellowGreenMin = Convert.ToInt32(sdr["kelly1"].ToString());
                    _colorParams.yellowGreenMax = Convert.ToInt32(sdr["kelly2"].ToString());
                    _colorParams.yellowMin = Convert.ToInt32(sdr["yellow1"].ToString());
                    _colorParams.yellowMax = Convert.ToInt32(sdr["yellow2"].ToString());
                    _colorParams.orangeRedMin = Convert.ToInt32(sdr["orange1"].ToString());
                    _colorParams.orangeRedMax = Convert.ToInt32(sdr["orange2"].ToString());
                    _colorParams.redMin1 = Convert.ToInt32(sdr["red1"].ToString());
                    _colorParams.redMax1 = Convert.ToInt32(sdr["red2"].ToString());
                    _colorParams.redMin2 = Convert.ToInt32(sdr["red3"].ToString());
                    _colorParams.redMax2 = Convert.ToInt32(sdr["red4"].ToString());
                    _colorParams.areaMin = Convert.ToDouble(sdr["area1"].ToString());
                    _colorParams.areaMax = Convert.ToDouble(sdr["area2"].ToString());
                    
                    /*this.area3 = sdr["area3"].ToString();
                    this.area4 = sdr["area4"].ToString();*/

                }
            }
            catch (Exception ex)
            {
                ShowErrorNotifier(ex.Message);
            }
            finally
            {
                sdr.Close();
            }

        }




        /// <summary>
        /// 鼠标从控件中移出
        /// </summary>
        /// <param name="index"></param>
        private void OnZoomPicMouseLeave(int index)
        {
            for (int i = 0; i < 8; i ++)
            {
                if (index == this.ZoomPics[i].Index && index != this.CurrActiveZoomPicIndex)
                {
                    this.ZoomPics[i].BackColor = Color.White;
                    break;
                }
            }
        }

        private void OnSelectReportPic(int slide_index)
        {
            if (this.addSample.ModelArr[slide_index].Status != 5)
            {
                this.ShowErrorTip("请先完成计算！");
                return;
            }
            ReportPic s = new ReportPic(this.addSample.ModelArr[slide_index].Id);
            DialogResult res = s.ShowDialog();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="slide_index"></param>
        private void OnShowPicZoom(int slide_index)
        {
            if (this.addSample.ModelArr[slide_index].Status != 5)
            {
                this.ShowErrorTip("请先完成拍摄和计算！");
                return;
            }
            this.CurrSlideIndex = slide_index;
            this.CurrPicFirstIndex = 0;

            this.ResetZoomPic();
            for (int i = 1; i < 7; i ++)
            {
                this.SetZoomPic(i, i-1);
            }
            this.SetActiveZoomPic(1, Image.FromFile(this.addSample.ModelArr[slide_index].PicInfoList[0].PicPath));
            //this.MainPic.Image = Image.FromFile(this.addSample.ModelArr[slide_index].PicInfoList[0].PicPath);
        }

        private void OnSnap(byte[] buffer)
        {
            this.MsgQueue.Enqueue(buffer);
            this.m_SyncContext.Post(this.ConsumeMsg, null);
        }

        private void ConsumeMsg(Object o)
        {
            
            while (this.MsgQueue.TryDequeue(out this.MsgBuffer))
            {
                int point_index_len = MsgBuffer[3] == 44 ? 1 : 2;
                int point_index = Convert.ToInt32(Encoding.Default.GetString(MsgBuffer, 2, point_index_len));
                int y = (point_index-1) / GlobalProperty.Cols;
                int slide_index = Convert.ToInt32(Encoding.Default.GetString(MsgBuffer, 0, 1)) - 1;
                
                if (this.CurrSnapSlideIndex != slide_index)
                {
                    this.ResetZoomPic();
                    if (this.CurrSnapSlideIndex != -1)
                    {
                        this.addSample.ModelArr[this.CurrSnapSlideIndex].Status = 4;
                    }
                    this.CurrSnapSlideIndex = slide_index;
                    this.CurrSlideIndex = slide_index;
                }
                string path = Encoding.Default.GetString(this.MsgBuffer, 3 + point_index_len, this.MsgBuffer.Length - point_index_len - 3);

                string outPath = path.Replace("\\images\\", "\\simages\\");
                //Log.Info(path + " === " +outPath);
                try
                {
                    //bool res = CheckNum.CellAnalyzeColorX20(ref _colorParams, ref _cellParams, path, outPath);
                    int size = Marshal.SizeOf(typeof(ReUnit)) * 1000;
                    IntPtr pBuff = Marshal.AllocHGlobal(size);
                    int cellNum = 0;

                    //返回精子详细的
                    if (!CheckNum.CellAnalyzeColorListX20(ref _colorParams, ref _cellParams, path, pBuff, ref cellNum))
                    {
                        ShowErrorTip("没有识别到细胞");
                    }
                    else
                    {
                        PictureInfo info = new PictureInfo
                        {
                            CellNum = _cellParams.totalNum,
                            PicPath = path.Replace(AppDir + "\\", ""),
                            SpicPath = outPath.Replace(AppDir + "\\", ""),
                            RedNum = _cellParams.redNum,
                            GreenNum = _cellParams.greenNum,
                            YellowNum = _cellParams.yellowNum,
                            OrangeNum = _cellParams.orangeNum,
                            KellyNum = _cellParams.yellowGreenNum,
                            X = y % 2 == 0 ? (point_index - 1) % GlobalProperty.Cols : (GlobalProperty.Cols - 1 - (point_index - 1) % GlobalProperty.Cols),
                            Y = y,
                        };
                        this.addSample.ModelArr[this.CurrSnapSlideIndex].TotalNum += _cellParams.totalNum;
                        this.addSample.ModelArr[this.CurrSnapSlideIndex].RedNum += _cellParams.redNum;
                        this.addSample.ModelArr[this.CurrSnapSlideIndex].YellowNum += _cellParams.yellowNum;
                        this.addSample.ModelArr[this.CurrSnapSlideIndex].GreenNum += _cellParams.greenNum;
                        this.addSample.ModelArr[this.CurrSnapSlideIndex].OrangeNum += _cellParams.orangeNum;
                        this.addSample.ModelArr[this.CurrSnapSlideIndex].KellyNum += _cellParams.yellowGreenNum;
                        this.addSample.ModelArr[this.CurrSnapSlideIndex].PicInfoList.Add(info);

                        this.SlidePanel.Invalidate();
                        this.addZoomPic();
                        if (point_index == GlobalProperty.Cols * GlobalProperty.Rows)
                        {
                            this.addSample.ModelArr[this.CurrSnapSlideIndex].Status = 4;
                            DFI_Test_SCD model = this.addSample.ModelArr[this.CurrSnapSlideIndex];

                            this.addSample.ModelArr[this.CurrSnapSlideIndex].ErrorCellNum = (model.RedNum + model.YellowNum + model.OrangeNum).ToString();
                            this.addSample.ModelArr[this.CurrSnapSlideIndex].DFI_Value = model.TotalNum == 0 ? 0f : Math.Round((model.RedNum + model.YellowNum + model.OrangeNum) * 100.0 / model.TotalNum, 2);
                            this.FlushSamplePanelData(slide_index);
                        }
                        if (this.SnapPointSize == ++this.SnapedPointSize)
                        {
                            this.SnapFinish();
                        }


                        //PictureInfo info1 = pictureInfos.Last();
                        //info1.ReUnits = new ReUnit[cellNum];
                        //for (int i = 0; i < cellNum; i++)
                        //{
                        //    IntPtr pr = new IntPtr(pBuff.ToInt64() + Marshal.SizeOf(typeof(ReUnit)) * i);
                        //    info1.ReUnits[i] = (ReUnit)Marshal.PtrToStructure(pr, typeof(ReUnit));

                        //    //cp.totalNum++;
                        //    //if (info.ReUnits[i].type == 1) cp.greenNum++;
                        //    //else if (info.ReUnits[i].type == 2) cp.yellowGreenNum++;
                        //    //else if (info.ReUnits[i].type == 3) cp.yellowNum++;
                        //    //else if (info.ReUnits[i].type == 4) cp.orangeNum++;
                        //    //else if (info.ReUnits[i].type == 5) cp.redNum++;
                        //}
                        //Marshal.FreeHGlobal(pBuff);
                        ////m_DTS.TotalNum += cp.totalNum;
                        ////m_DTS.RedNum += cp.redNum;
                        ////m_DTS.YellowNum += cp.yellowNum;
                        ////m_DTS.GreenNum += cp.greenNum;
                        ////m_DTS.OrangeNum += cp.orangeNum;
                        ////m_DTS.KellyNum += cp.yellowGreenNum;
                        //pictureInfos[pictureInfos.Count - 1].CellNum = _cellParams.totalNum; ;
                        //pictureInfos[pictureInfos.Count - 1].RedNum = _cellParams.redNum;
                        //pictureInfos[pictureInfos.Count - 1].GreenNum = _cellParams.greenNum;
                        //pictureInfos[pictureInfos.Count - 1].YellowNum = _cellParams.yellowNum;
                        //pictureInfos[pictureInfos.Count - 1].OrangeNum = _cellParams.orangeNum;
                        //pictureInfos[pictureInfos.Count - 1].KellyNum = _cellParams.yellowGreenNum;
                    }
                }
                catch (Exception e)
                {

                }
                

                
                

            }
        }

        private static int _rgbSize = 1920 * 1200 * 3;
        public byte[] _rgbData = new byte[_rgbSize];



        private int icdev = 0;//设备号

        /// <summary>
        /// 点击新建下一组样本
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void newSampBtn_Click(object sender, EventArgs e)
        {
            if (this.DeviceStatus == 2)
            {
                ShowErrorTip("设备正在运行，请稍后");
                return;
            }
            if (this.DeviceStatus == 3 && !ShowAskDialog("确认进行下一批样本检测吗？"))
            {
                return;
            }
            if (this.DeviceStatus < 1 && !ShowAskDialog("此操作会清空面板中已编辑的样本信息，确认继续？"))
            {
                return;
            }
            for (int i = 0; i < 5; i ++)
            {
                addSample.ModelArr[i] = DFI_Test_SCD.GetInstance();
                this.FlushSamplePanelData(i);
            }
            this.DeviceStatus = 0;
            this.SnapedPointSize = 0;
            this.SnapPointSize = 0;
            this.SnapedPointSize = 0;
            this.SnapPointSize = 0;
            this.SlidePanel.Invalidate();
            this.ResetZoomPic();
            this.MainPic.Image = null;
            this.addSample.OpenEditSamplePage(0);
        }

        /// <summary>
        /// 更新样本信息
        /// </summary>
        /// <param name="index"></param>
        /// <param name="flag"> 0: 样本信息，1 ：样本信息 + 细胞总数  2 ：样本信息 + DFI结果</param>
        private void FlushSamplePanelData(int index)
        {
            DFI_Test_SCD model = this.addSample.ModelArr[index];
            this.SampleControls[index].SetData(model.Status, model.Patient_ID, model.Test_ID,int.Parse(model.RightCellNum) , int.Parse(model.ErrorCellNum) , model.TotalNum, (double)model.DFI_Value,
                model.Patient_Name, model.Patient_Age.ToString(), model.Sample_Type, model.Doctor_Name, model.Tester, model.Send_Date.ToString("yyyy-MM-dd")
                ,model.Test_Date.ToString("yyyy-MM-dd"), model.Reviewer, model.Remark);
        }

        static readonly string AppDir = Environment.CurrentDirectory;


        /// <summary>
        /// 接收拍照完成
        /// </summary>
        private void SnapFinish()
        {
            this.ShowSuccessNotifier("拍摄结束");   
            this.CurrSnapSlideIndex = -1;
            this.DeviceStatus = 3;
            bool isFirst = false;
            #region 扣卡
          /*  CardMag cm = new CardMag();
            string infoMsg = "";
            int restNumone = 0;
            int calc_count = 0;
            for (int i = 0; i < 5; i++)
            {
                if (this.addSample.ModelArr[i].Status == 4)
                {
                    calc_count++;
                }
            }
            bool read_success = false;
            bool rest_num_enough = false;
            do
            {
                try
                {
                    if (!(read_success = cm.ReadCard(ref infoMsg, ref restNumone)))
                    {
                        mifareone.rf_exit(icdev);
                        throw new Exception();
                    }
                    else if (!(rest_num_enough = restNumone > calc_count))
                    {
                        this.ShowErrorDialog("卡次不足，请换卡再计算！");
                    }
                }
                catch (Exception e)
                {
                    this.ShowErrorDialog("读卡失败, 返回信息：[" + infoMsg + "]。\n 请确定读卡器或授权卡是否连接放置正常！");
                }
            }
            while (!rest_num_enough || !read_success);
            restNumone -= calc_count;
            if (!cm.WriteCard2(ref infoMsg, calc_count))
            {
                MessageBox.Show(infoMsg, "友情提示");
                mifareone.rf_exit(icdev);
                return;
            }*/
            #endregion
            
            #region LIS result 更新 准备
            string excelReportPath1 = "LIS Results" + "/" + DateTime.Now.ToString("yyyy") + "/" + DateTime.Now.ToString("MM") + "/";
            string excelReportPath = "Reports" + "/" + DateTime.Now.ToString("yyyy") + "/" + DateTime.Now.ToString("MM") + "/";
            if (!Directory.Exists(AppDomain.CurrentDomain.BaseDirectory + excelReportPath)) Directory.CreateDirectory(AppDomain.CurrentDomain.BaseDirectory + excelReportPath);
            if (!Directory.Exists(AppDomain.CurrentDomain.BaseDirectory + excelReportPath1)) Directory.CreateDirectory(AppDomain.CurrentDomain.BaseDirectory + excelReportPath1);
            // excelReportPath
            string excelreports = "tempFile\\DFI_SCD_TEST.xls";
            // string excelfile = m_DT.Patient_ID + " " +m_DT.Patient_Name;//文件名为病人姓名
            string excelFile = excelReportPath + DateTime.Now.ToString("yyyyMMdd") + " " + "DFI report.xls";
            string excelFile1 = excelReportPath1 + DateTime.Now.ToString("yyyyMMdd") + " " + "DFI report.xls";
            string excelFilepath = AppDomain.CurrentDomain.BaseDirectory + excelFile;
            string excelFilepath1 = AppDomain.CurrentDomain.BaseDirectory + excelFile1;
            if (!File.Exists(excelFilepath)) File.Copy(excelreports, excelFilepath);
            FileStream file = new FileStream(@excelFilepath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
            POIFSFileSystem ps = new POIFSFileSystem(file);//需using NPOI.POIFS.FileSystem;
            IWorkbook workbook = new HSSFWorkbook(ps);
            ISheet sheet = workbook.GetSheet("DFI_SCD_Test");//获取工作表
            FileStream fout = new FileStream(@excelFilepath, FileMode.Open, FileAccess.Write, FileShare.ReadWrite);//写入流
            #endregion

            DataTable dt2 = dal.GetDataTable("select max(id) max_id from PictureInfo", "max_id");
            string max_id_str = dt2.Rows[0]["max_id"].ToString();
            int max_id = max_id_str == "" ? 0 : Convert.ToInt32(max_id_str);
            for (int i = 0; i < 5; i ++)
            {

                try
                {

                    if (this.addSample.ModelArr[i].Status != 4) continue;
                    this.addSample.ModelArr[i].Status = 5;
                    DFI_Test_SCD model = this.addSample.ModelArr[i];
                    // 数据展示
                    this.FlushSamplePanelData(i);
                    // 在显示面板上展示第一个样本的图片
                    if (!isFirst)
                    {
                      /*  this.PrePicBtn.Location = new Point(20, 55);
                        this.NextPicBtn.Location = new Point(536, 55);*/
                        this.CurrSlideIndex = i;
                        this.CurrPicFirstIndex = 0;
                        this.MainPic.Image = Image.FromFile((this.addSample.ModelArr[i].PicInfoList[0].PicPath));
                        this.ResetZoomPic();
                        for (int j = 0; j < 6; j++)
                        {
                            this.SetZoomPic(j + 1, j); 
                        }
                        isFirst = true;
                    }


                    if (!string.IsNullOrEmpty(model.Tester) && !("#" + GlobalProperty.Testers + "#").Contains("#" + model.Tester + "#"))
                    {
                        GlobalProperty.Testers = GlobalProperty.Testers + "#" + model.Tester;
                    }
                    if (!string.IsNullOrEmpty(model.Doctor_Name) && !("#" + GlobalProperty.Doctors + "#").Contains("#" + model.Doctor_Name + "#"))
                    {
                        GlobalProperty.Doctors = GlobalProperty.Doctors + "#" + model.Doctor_Name;
                    }
                    if (!string.IsNullOrEmpty(model.Reviewer) && !("#" + GlobalProperty.Reviewers + "#").Contains("#" + model.Reviewer + "#"))
                    {
                        GlobalProperty.Reviewers = GlobalProperty.Reviewers + "#" + model.Reviewer;
                    }
                    String basePath = "images" + "\\" + DateTime.Now.ToString("yyyyMMdd") + "\\" + model.Patient_ID + "_" + model.Test_ID + "\\" + "report";
                    if (!Directory.Exists(AppDir + "\\" + basePath + "\\"))
                        Directory.CreateDirectory(AppDir + "\\" + basePath + "\\");
                    // 存储报告图， 默认取前三张图的最中间；
                    
                    for (int j = 0; j < model.PicInfoList.Count; j ++)
                    {
                        model.PicInfoList[j].Id = ++max_id;
                    }
                    model.PicInfoList.Sort((v1, v2) => v2.CellNum - v1.CellNum);
                    for (int j = 0; j < 4; j++)
                    {
                        model.ImageName[j] = basePath + "\\p_" + i + ".jpg";
                        Ptimage.CaptureImage(model.PicInfoList[j].PicPath, 710, 350, model.ImageName[j], 500, 500);
                    }
                    #region//数据插入
                    String strSql = "insert into DFI_Test_SCD([Patient_ID],[Test_ID],[Patient_Name],[Patient_Age],[Sample_Type],[Tester],"
                        + "[Reviewer],[Doctor_Name],[Send_Date],[Test_Date],[DFI_Value],[redNum],[yellowNum],[greenNum],[orangeNum],[kellyNum],[totalNum],[Remark],"
                        + "[image1],[image2],[image3],[image4],[pic1_id],[pic2_id],[pic3_id],[lis_status])values('"
                        + model.Patient_ID + "', '" + model.Test_ID + "', '" + model.Patient_Name + "',' " + (model.Patient_Age != 0 ? model.Patient_Age.ToString() : "") + "', '"
                        + model.Sample_Type + "', '" + model.Tester + "', '" + model.Reviewer + "', '" + model.Doctor_Name + "', '" + model.Send_Date.ToString(CultureInfo.InvariantCulture) + "', '"
                        + model.Test_Date.ToString(CultureInfo.InvariantCulture) + "'," + model.DFI_Value + ", '" + model.RedNum + "','"
                        + model.YellowNum + "','" + model.GreenNum + "','" + model.OrangeNum + "','" + model.KellyNum + "','" + model.TotalNum + "','" + model.Remark + "','" 
                        + model.ImageName[0] + "', '" + model.ImageName[1] + " ', '" + model.ImageName[2] + "', '" + " ', '" + model.ImageName[3] + "', '" + model.PicInfoList[0].Id
                        + "','" + model.PicInfoList[1].Id + "','" + model.PicInfoList[2].Id + "', 0);select last_insert_rowid() a;";
                    
                    DataTable dt = dal.GetDataTable(strSql, "test");
                    DataRow dataRow = dt.AsEnumerable().First();
                    List<object> lt = dataRow.ItemArray.ToList();
                    if (lt != null)
                    {
                        model.Id = Convert.ToInt32(lt[0].ToString());
                    }
                    List<string> sqls = new List<string>();
                    //保存图片路径
                    model.PicInfoList.Sort((v1, v2) => v1.Id - v2.Id);
                    for (int j = 0; j < model.PicInfoList.Count; j++)
                    {
                        sqls.Add("insert into PictureInfo(id, pid,picPath,spicPath,cellNum,redNum,yellowNum,greenNum,orangeNum,kellyNum) values('"
                            + model.PicInfoList[j].Id + "','" + model.Id + "','" + model.PicInfoList[j].PicPath + "','"
                            + model.PicInfoList[j].SpicPath + "','" + model.PicInfoList[j].CellNum + "','" + model.PicInfoList[j].RedNum + "','" + model.PicInfoList[j].YellowNum + "','"
                            + model.PicInfoList[j].GreenNum + "','" + model.PicInfoList[j].OrangeNum + "','" + model.PicInfoList[j].KellyNum + "')");
                        //Log.Info(sqls[j]);
                    }
                    dal.ExecDataBySqls(sqls);
                    #endregion

                    #region Lis Result 插入
                    IRow SheetRow = sheet.CreateRow((sheet.LastRowNum + 1));//在工作表中添加一行
                    HSSFCell[] SheetCell = new HSSFCell[25];
                    sheet.CreateRow((sheet.LastRowNum));
                    for (int k = 0; k < 25; k++)
                    {
                        SheetCell[k] = (HSSFCell)SheetRow.CreateCell(k);  //为第一行创建21个单元格
                    }
                    SheetCell[0].SetCellValue(sheet.LastRowNum);
                    SheetCell[1].SetCellValue(model.Patient_ID);
                    SheetCell[2].SetCellValue(model.Test_ID);
                    SheetCell[3].SetCellValue(model.Patient_Name);
                    SheetCell[4].SetCellValue(model.Patient_Age);
                    SheetCell[5].SetCellValue(model.Doctor_Name);
                    SheetCell[6].SetCellValue(model.Sample_Type);
                    SheetCell[7].SetCellValue(model.Tester);
                    SheetCell[8].SetCellValue(model.Reviewer);
                    SheetCell[9].SetCellValue(model.Send_Date.ToString(CultureInfo.InvariantCulture).Substring(0, 10));
                    SheetCell[10].SetCellValue(model.Test_Date.ToString(CultureInfo.InvariantCulture).Substring(0, 10));
                    SheetCell[11].SetCellValue((double)model.DFI_Value);
                    SheetCell[12].SetCellValue((model.GreenNum + model.KellyNum));
                    SheetCell[13].SetCellValue(model.RedNum + model.YellowNum + model.OrangeNum);
                    SheetCell[14].SetCellValue(model.TotalNum);
                    SheetCell[15].SetCellValue(model.ImageName[0]);
                    SheetCell[16].SetCellValue(model.ImageName[1]);
                    SheetCell[17].SetCellValue(model.ImageName[2]);
                    SheetCell[18].SetCellValue(model.ImageName[3]);

                    SheetCell[19].SetCellValue(model.Remark);
                    #endregion
                }
                catch (Exception e)
                {
                    Log.Info(e.Message);
                }
                this.FlushSamplePanelData(i);

            }
            fout.Flush();
            workbook.Write(fout);//写入文件
            workbook.Close();
            fout.Close();
            File.Copy(excelFilepath, excelFilepath1, true);
            this.ShowSuccessNotifier("计算完成");

            
        }

        /// <summary>
        /// 进入编辑样本信息弹窗
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void EditSample(int index)
        {
            if (this.DeviceStatus == 2)
            {
                this.ShowErrorTip("设备运行中，信息不可编辑");
                return;
            }
            else if (this.DeviceStatus == 3)
            {
                if (this.ShowAskDialog("改样本已计算完毕，重新录入下一批样本信息？"))
                {
                    for (int i = 0; i < 5; i++)
                    {
                        addSample.ModelArr[i] = DFI_Test_SCD.GetInstance();
                        this.FlushSamplePanelData(i);
                    }
                    this.DeviceStatus = 0;
                    this.SnapedPointSize = 0;
                    this.SnapPointSize = 0;
                    this.SnapedPointSize = 0;   
                    this.SnapPointSize = 0;
                    this.SlidePanel.Invalidate();
                    this.ResetZoomPic();
                    this.MainPic.Image = null;
                    this.addSample.OpenEditSamplePage(0);
                }
                return;
            }
            this.addSample.OpenEditSamplePage(index);
        }

        // 320 X 240 


        /// <summary>
        /// 报告打印
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Print(int index)
        {
            if (this.addSample.ModelArr[index].Status != 5)
            {
                this.ShowErrorTip("未计算，无法打印");
                return;
            }
            this.CreateReport(index, true);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="index"></param>
        /// <param name="open_flag">0: 打开 1：</param>
        private void CreateReport(int index, bool open_flag)
        {
            string PrintMsg = null;
            string[] PatientInfoAddr = new string[]{
            this.addSample.ModelArr[index].Patient_ID,
            this.addSample.ModelArr[index].Test_ID,
            this.addSample.ModelArr[index].Patient_Name,
            this.addSample.ModelArr[index].Patient_Age.ToString().Equals("0") ? "" : this.addSample.ModelArr[index].Patient_Age.ToString(),
            this.addSample.ModelArr[index].Send_Date.ToString("yyyy-MM-dd"),
            this.addSample.ModelArr[index].Doctor_Name,
            this.addSample.ModelArr[index].Test_Date.ToString("yyyy-MM-dd"),
            this.addSample.ModelArr[index].Sample_Type.Equals("新鲜样本") ? "√" : " ",
            this.addSample.ModelArr[index].Sample_Type.Equals("冻存样本") ? "√" : "",
            "男",
            this.addSample.ModelArr[index].DFI_Value.ToString(),
            GlobalProperty.Hospital,
            this.addSample.ModelArr[index].Tester,
            this.addSample.ModelArr[index].Reviewer,
            this.addSample.ModelArr[index].Remark,
            this.addSample.ModelArr[index].GreenNum.ToString(),
            (100.0 - this.addSample.ModelArr[index].DFI_Value).ToString(),
            (this.addSample.ModelArr[index].KellyNum + this.addSample.ModelArr[index].YellowNum + this.addSample.ModelArr[index].OrangeNum + this.addSample.ModelArr[index].RedNum).ToString(),
            this.addSample.ModelArr[index].DFI_Value.ToString(),
            this.addSample.ModelArr[index].TotalNum.ToString()};
            if (!ShowReport.printReport(PatientInfoAddr, this.addSample.ModelArr[index].ImageName[0], this.addSample.ModelArr[index].ImageName[1], this.addSample.ModelArr[index].ImageName[2], this.addSample.ModelArr[index].ImageName[3], this.addSample.ModelArr[index].GreenNum, this.addSample.ModelArr[index].KellyNum, this.addSample.ModelArr[index].YellowNum, this.addSample.ModelArr[index].OrangeNum, this.addSample.ModelArr[index].RedNum, true, ref PrintMsg))
            {
                ShowErrorDialog(PrintMsg ?? "打印失败");
            }
        }

        private void uiTitlePanel1_Paint(object sender, PaintEventArgs e)
        {
            for (int i = 0; i < 5; i ++)
            {
                this.SampleControls[i].Location = new Point(45, 70 + i * 65);

            }
        }

        private void uiTitlePanel1_Paint(object sender, EventArgs e)
        {

        }

       /* private void InitAdaptor(object try_time)
        {
            int i = 0;
            while ((this.InitResult = Adaptor.AutoConnect()) != 0 && i++ < Convert.ToInt32(try_time))
            {
                Thread.Sleep(5);
            }
            if (this.InitResult == 0)
            {
                //ShowSuccessNotifier("设备连接成功");
                this.m_SyncContext.Send(_ShowSuccessNotifier, "设备连接成功");
            }
            else
            {
                this.m_SyncContext.Send(_ShowErrorNotifier, "设备连接失败");
                //this.ShowErrorNotifier("设备连接失败了");
            }
        }*/

        private void _ShowErrorNotifier(object msg)
        {
            this.ShowErrorNotifier((string)msg);
        }
        private void _ShowSuccessNotifier(object msg)
        {
            this.ShowSuccessNotifier((string)msg);
        }

        private void PagePanel_Load(object sender, EventArgs e)
        {
            this.InitSlidePanelParams();
            this.uiToolTip1.SetToolTip(uiSymbolButton1, "新建下一组实验");
            this.uiToolTip1.SetToolTip(uiSymbolButton2, "打开报告文件夹");
           /* Thread thread = new Thread(InitAdaptor)
            {
                IsBackground = true
            };
            thread.Start(1);*/
        }

        #region 字段
        /// <summary>
        /// SlidePanel 控件的宽度
        /// </summary>
        private int _slidePanel_width;
        /// <summary>
        /// SlidePanel 控件的height
        /// </summary>
        private int _slidePanel_height;

        /// <summary>
        /// 进度条圆角半径
        /// </summary>
        private int _progress_raduis = 15;
        /// <summary>
        /// 进度条高度
        /// </summary>
        private int _progress_height = 16;

        private int _progress_width;
        /// <summary>
        /// 进度条location Y
        /// </summary>
        private int _progress_y;
        /// <summary>
        /// 进度条location X
        /// </summary>
        private int _progress_x;

        /// <summary>
        /// slidePanel 与玻片的左右内边距
        /// </summary>
        private int _slidePanelPadding = 35;
        /// <summary>
        /// 玻片与玻片的左右间距
        /// </summary>
        private int _slideMargin = 20;
        /// <summary>
        /// 玻片width之和
        /// </summary>
        private int _slideWidthSum;
        /// <summary>
        /// 玻片上部颜色（正常）
        /// </summary>
        private Brush _topBrush1 = new SolidBrush(Color.FromArgb(100, 173, 216, 230));
        /// <summary>
        /// 玻片上部颜色（特写）
        /// </summary>
        private Brush _topBrush2 = new SolidBrush(Color.FromArgb(150, 173, 216, 230));
        /// <summary>
        /// 玻片下部颜色
        /// </summary>
        private Brush _bottomBrush = new SolidBrush(Color.FromArgb(222, 222, 222));

        private Brush _emptyTextBrush = new SolidBrush(Color.FromArgb(176, 176, 176));
        
        /// <summary>
        /// 白色画刷
        /// </summary>
        private Brush _whiteBrush = new SolidBrush(Color.White);
        /// <summary>
        /// 钢蓝色画刷
        /// </summary>
        private Brush _steelBlueBrush = new SolidBrush(_steelColor);

        private Brush _activeSteelBrush = new SolidBrush(_activeSteelColor);
        private Pen _activeSteelPen2 = new Pen(_activeSteelColor, 2);
        /// <summary>
        /// 点位拍摄完之后的颜色
        /// </summary>
        private Pen _steelBluePen3 = new Pen(_steelColor, 3);
        private Pen _steelBluePen2 = new Pen(_steelColor, 2);
        private Pen _steelBluePen1 = new Pen(_steelColor);

        static Font _slideTextFont  = new Font("微软雅黑",12f, FontStyle.Bold);

        private Pen _emptySlidePen = new Pen(Color.FromArgb(222, 222, 222), 3);

        static Color _steelColor = Color.FromArgb(70, 130, 180);

        static Color _steelColor2 = Color.FromArgb(188, 70, 130, 180);

        static Color _activeSteelColor = Color.FromArgb(70, 140, 210);

        /// <summary>
        ///  掩盖进度条左端超度的部分
        /// </summary>
        static GraphicsPath _progress_extend_gp;


        static GraphicsPath _progress_btn_left_gp;
        static GraphicsPath _progress_btn_right_gp;
        static GraphicsPath _progress_btn_gp;


        static GraphicsPath _progress_gp;

        private int _progress_btn_width;
        private int _progress_btn_height=35;

        #endregion
        private void InitSlidePanelParams()
        {
            this._slidePanel_height = this.SlidePanel.Height;
            this._slidePanel_width = this.SlidePanel.Width;
            this._slideWidthSum = this._slidePanel_width - _slidePanelPadding * 2 - _slideMargin * 4;
            this._progress_width = this._slidePanel_width / 2;
            this._progress_y = _slidePanel_height - _progress_height - 45;
            this._progress_x = (int)(this._slidePanel_width * 0.25);
            int l = 2 * this._progress_raduis;
            int bottom_y = this._progress_y + this._progress_height;
            _progress_extend_gp = new GraphicsPath();
            _progress_extend_gp.AddLine(new Point(this._progress_x - this._progress_raduis, this._progress_y - this._progress_raduis), new Point(this._progress_x + this._progress_raduis, this._progress_y));
            _progress_extend_gp.AddArc(new Rectangle(this._progress_x, this._progress_y, l, l), 180F, 90F);
            _progress_extend_gp.AddLine(new Point(this._progress_x, this._progress_y + this._progress_raduis), new Point(this._progress_x, bottom_y - this._progress_raduis));
            _progress_extend_gp.AddArc(new Rectangle(this._progress_x, bottom_y - l, l, l), 180F, -90F);
            _progress_extend_gp.AddLine(new Point(this._progress_x + this._progress_raduis, bottom_y), new Point(this._progress_x - this._progress_raduis, bottom_y + this._progress_raduis));
            _progress_extend_gp.AddLine(new Point(this._progress_x - this._progress_raduis, bottom_y + this._progress_height), new Point(this._progress_x - this._progress_raduis, this._progress_y - this._progress_raduis));
          
            _progress_gp = this.GetRectangleGp(this._progress_x, this._progress_y, this._progress_width, this._progress_height, this._progress_raduis);
            this._progress_btn_width = this._slidePanel_width *2 / 5;
            this.ProgressBtn.Width = this._progress_btn_width;
            this.ProgressBtn.Height = this._progress_btn_height;
            this.ProgressBtn.Location = new Point((_slidePanel_width - _progress_btn_width) / 2, _slidePanel_height - _progress_btn_height - 40);
            int pro_width = this._progress_btn_width - 4;
            int pro_height = this._progress_btn_height - 4;
            int offset = 2;
            _progress_btn_left_gp = new GraphicsPath();
            _progress_btn_left_gp.AddLine(new Point(-_progress_raduis+ offset, -_progress_raduis + offset), new Point(this._progress_raduis + offset, offset));
            _progress_btn_left_gp.AddArc(new Rectangle(offset, offset, l, l), 270F, -90F);
            _progress_btn_left_gp.AddLine(new Point(offset, _progress_raduis + offset), new Point(offset, pro_height - _progress_raduis + offset));
            _progress_btn_left_gp.AddArc(new Rectangle(offset, pro_height - l + offset, l, l), 180F, -90F);
            _progress_btn_left_gp.AddLine(new Point(_progress_raduis + offset, pro_height + offset), new Point(-_progress_raduis + offset, pro_height + _progress_raduis + offset));
            _progress_btn_left_gp.AddLine(new Point(-_progress_raduis + offset, pro_height + _progress_raduis + offset), new Point(-_progress_raduis + offset, -_progress_raduis + offset));
            _progress_btn_right_gp = new GraphicsPath();
            _progress_btn_right_gp.AddLine(new Point(pro_width + _progress_raduis + offset, -_progress_raduis + offset), new Point(pro_width - _progress_raduis + offset, +offset));
            _progress_btn_right_gp.AddArc(new Rectangle(pro_width - l + offset, +offset, l, l), 270F, 90F);
            _progress_btn_right_gp.AddLine(new Point(pro_width + offset, _progress_raduis + offset), new Point(pro_width + offset, pro_height - _progress_raduis + offset));
            _progress_btn_right_gp.AddArc(new Rectangle(pro_width - l + offset, pro_height - l + offset, l, l), 0F, 90F);
            _progress_btn_right_gp.AddLine(new Point(pro_width + _progress_raduis + offset, pro_height + offset), new Point(pro_width + _progress_raduis + offset, pro_height + _progress_raduis + offset));
            _progress_btn_right_gp.AddLine(new Point(pro_width + _progress_raduis + offset, pro_height + _progress_raduis + offset), new Point(pro_width + _progress_raduis + offset, -_progress_raduis + offset));
            _progress_btn_gp = GetRectangleGp(offset, offset, pro_width, pro_height, _progress_raduis);
        }


        private void OnZoomPicClick(int index, Image image)
        {
            if (this.DeviceStatus != 3) return;
            this.SetActiveZoomPic(index, image);
        }

        int CurrActiveZoomPicIndex = -1;

        /// <summary>
        /// 移入
        /// </summary>
        /// <param name="index"></param>
        private void OnZoomPicEnter(int index)
        {
            //if (this.DeviceStatus != 3) return;
            for (int i = 0; i < this.ZoomPics.Length; i++)
            {
                if (this.ZoomPics[i].Index == this.CurrActiveZoomPicIndex || this.ZoomPics[i].Image == null)
                    continue;
                if (this.ZoomPics[i].Index == index)
                {
                    this.ZoomPics[i].BackColor = Color.LightBlue;
                }
                else if (!this.ZoomPics[i].BackColor.Equals(Color.White))
                {
                    this.ZoomPics[i].BackColor = Color.White;
                }
            }
        }

        /// <summary>
        /// 第一张缩放图上显示的图片的索引
        /// </summary>
        private int CurrPicFirstIndex;

        /// <summary>
        /// 当前显示缩放图的玻片对应索引
        /// </summary>
        private int CurrSlideIndex;

        /// <summary>
        /// 平移
        /// </summary>
        /// <param name="direction">方向 0： 左移-5； 1：右移+5</param>
        private void Translate(int direction, int step_num)
        {

            int step_range = 170 / step_num;
            int last_step_range = 170 - step_range * (step_num - 1);

            int len = this.ZoomPics.Length;
            int pic_count = this.addSample.ModelArr[this.CurrSlideIndex].PicInfoList.Count;
            if (direction == 0)
            {
                step_range = -step_range;
                last_step_range = -last_step_range;
                // 取模
                int temp_index = (this.CurrPicFirstIndex + len - 2) % pic_count;
                for (int j = 0; j < len; j++)
                {
                    if (this.ZoomPics[j].Index == len - 1)
                    {
                        this.SetZoomPic(j, temp_index);
                        break;
                    }
                }
                this.CurrPicFirstIndex = this.CurrPicFirstIndex == pic_count - 1 ? 0 : this.CurrPicFirstIndex + 1;
            }
            else
            {
                int temp_index = (this.CurrPicFirstIndex == 0 ? pic_count : this.CurrPicFirstIndex) - 1;
                for (int j = 0; j < len; j++)
                {
                    if (this.ZoomPics[j].Index == 0)
                    {
                        this.SetZoomPic(j, temp_index);
                        break;
                    }
                }
                this.CurrPicFirstIndex = (this.CurrPicFirstIndex == 0 ? pic_count : this.CurrPicFirstIndex) - 1;
            }

            for (int i = 0; i < step_num; i++)
            {
                for (int j = 0; j < len; j++)
                {
                    this.ZoomPics[j].Location = new Point(this.ZoomPics[j].Location.X + (i == step_num - 1 ? last_step_range : step_range), 0);
                    if (i == step_num - 1)
                    {
                        if (direction == 0)
                        {
                            if (--this.ZoomPics[j].Index == -1)
                            {
                                this.ZoomPics[j].Index = len - 1;
                                this.ZoomPics[j].Location = new Point((len - 2) * 170, 0);
                            }
                        }
                        else
                        {
                            if (++this.ZoomPics[j].Index == len)
                            {
                                this.ZoomPics[j].Index = 0;
                                this.ZoomPics[j].Location = new Point(-170, 0);
                            }
                        }
                    }
                }
                this.PicSetPanel.Update();
            }
        }

        /// <summary>
        ///  点左边按钮 左出右隐
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolLabel2_Click(object sender, EventArgs e)
        {
            if(this.DeviceStatus != 3) return;
            if (this.CurrActiveZoomPicIndex == -1)
            {
                    this.Translate(1,6);
            }
            else if (this.CurrActiveZoomPicIndex == 1)
            {
                List<PictureInfo> list = this.addSample.ModelArr[this.CurrSlideIndex].PicInfoList;
                this.SetActiveZoomPic(0, Image.FromFile(list[this.CurrPicFirstIndex == 0 ? list.Count - 1 : this.CurrPicFirstIndex - 1].PicPath));
                this.Translate(1, 7);
                this.CurrActiveZoomPicIndex = 1;
            }
            else
            {
                List<PictureInfo> list = this.addSample.ModelArr[this.CurrSlideIndex].PicInfoList;
                this.SetActiveZoomPic(this.CurrActiveZoomPicIndex - 1, Image.FromFile(list[(this.CurrPicFirstIndex + this.CurrActiveZoomPicIndex-2) % (list.Count - 1) ].PicPath));
            }
           // List<PictureInfo> list1 = this.addSample.ModelArr[this.CurrSlideIndex].PicInfoList;
           // ShowAskDialog("CurrActiveZoomPicIndex: " + CurrActiveZoomPicIndex + " \nCurrPicFirstIndex:" + CurrPicFirstIndex + "\npicindex:" + (this.CurrPicFirstIndex + this.CurrActiveZoomPicIndex >= list1.Count - 1 ? (this.CurrPicFirstIndex + this.CurrActiveZoomPicIndex) % (list1.Count - 1) : this.CurrPicFirstIndex + this.CurrActiveZoomPicIndex + 1));
        }



        /// <summary>
        /// 点右边按钮 右出左隐
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolLabel3_Click(object sender, EventArgs e)
        {
            if (this.DeviceStatus != 3) return;
            if (this.CurrActiveZoomPicIndex == -1)
            {
                this.Translate(0,6);
            }
            else if (this.CurrActiveZoomPicIndex == 5 || this.CurrActiveZoomPicIndex == 6) {
                List<PictureInfo> list = this.addSample.ModelArr[this.CurrSlideIndex].PicInfoList;
                this.SetActiveZoomPic(this.CurrActiveZoomPicIndex + 1, Image.FromFile(list[(this.CurrPicFirstIndex + this.CurrActiveZoomPicIndex) % list.Count].PicPath));
                this.Translate(0,7);
                this.CurrActiveZoomPicIndex--;
            }
            else
            {
                List<PictureInfo> list = this.addSample.ModelArr[this.CurrSlideIndex].PicInfoList;
                this.SetActiveZoomPic(this.CurrActiveZoomPicIndex + 1, Image.FromFile(list[(this.CurrPicFirstIndex + this.CurrActiveZoomPicIndex) % list.Count].PicPath));
            }
           // List<PictureInfo> list1 = this.addSample.ModelArr[this.CurrSlideIndex].PicInfoList;
           // ShowAskDialog("CurrActiveZoomPicIndex: " + CurrActiveZoomPicIndex + " \nCurrPicFirstIndex:" + CurrPicFirstIndex + "\npicindex:" + (this.CurrPicFirstIndex + this.CurrActiveZoomPicIndex >= list1.Count - 1 ? (this.CurrPicFirstIndex + this.CurrActiveZoomPicIndex) % (list1.Count - 1) : this.CurrPicFirstIndex + this.CurrActiveZoomPicIndex + 1));
        }

        private void SetActiveZoomPic(int index, Image image)
        {
            this.MainPic.Image = image;
            this.CurrActiveZoomPicIndex = index;
            for (int i = 0; i < this.ZoomPics.Length; i++)
            {
                if (this.ZoomPics[i].Index == index && this.ZoomPics[i].BackColor != Color.BlueViolet) this.ZoomPics[i].BackColor = Color.BlueViolet;
                else if (this.ZoomPics[i].Index != index && this.ZoomPics[i].BackColor != Color.White) this.ZoomPics[i].BackColor = Color.White;
            }
        }

        /// <summary>
        /// 移入
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void PicSetPanel_MouseEnter(object sender, EventArgs e)
        {
            for (int i = 0; i < this.ZoomPics.Length; i++)
            {
                if (this.CurrActiveZoomPicIndex == this.ZoomPics[i].Index) continue;

                if (!this.ZoomPics[i].BackColor.Equals(Color.White))
                    this.ZoomPics[i].BackColor = Color.White;

               /* if (i != this.CurrActiveZoomPicIndex)
                {
                    this.ZoomPics[i].MouseLeave();
                }   */
            }
        }

        private void ResetZoomPic()
        {
            this.CurrPicFirstIndex = 0;
            this.CurrActiveZoomPicIndex = -1;
            for (int i = 0; i < this.ZoomPics.Length; i++)
            {
                this.ZoomPics[i].Index = i;
                this.ZoomPics[i].BackColor = Color.White;
                this.ZoomPics[i].Text = "";
                this.ZoomPics[i].Image = null;
                this.ZoomPics[i].Location = new Point((i - 1) * 170, 0);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="zoomPic_index">zoomPic 的index</param>
        /// <param name="pic_index">pic 索引</param>
        private void SetZoomPic(int zoomPic_id, int pic_index)
        {
            PictureInfo info = this.addSample.ModelArr[this.CurrSlideIndex].PicInfoList[pic_index];
            this.ZoomPics[zoomPic_id].Image = Image.FromFile(info.PicPath);
            this.ZoomPics[zoomPic_id].Text = "#" + (this.CurrSlideIndex + 1) + "【" + (info.Y + 1) + "," + (info.X + 1) + "】";
        }

        private void addZoomPic()
        {
            int pic_count = this.addSample.ModelArr[CurrSlideIndex].PicInfoList.Count;
            if (pic_count == 1) this.ResetZoomPic();
            if (pic_count <= 6)
            {

                for(int i = 1; i < this.ZoomPics.Length; i ++)
                {
                    if (this.ZoomPics[i].Index == pic_count)
                    {
                        SetZoomPic(i, pic_count - 1);
                        break;
                    }
                }
            }
            else if (pic_count > 6)
            {
                for (int i = 0; i < this.ZoomPics.Length; i++)
                {
                    if (this.ZoomPics[i].Index == 6)
                    {
                        SetZoomPic(i, pic_count - 1);
                        this.Translate(0, 4);
                        break;
                    }
                    
                }
            }
        }

        /// <summary>
        /// 打开报告所在文件夹
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton2_Click(object sender, EventArgs e)
        {
            if (this.DeviceStatus != 3)
            {
                this.ShowErrorTip("请先完成计算！");
                return;
            }
            for (int i = 0; i < 5; i ++)
            {
                if (this.addSample.ModelArr[i].Status == 5)
                {
                    this.CreateReport(i, false);
                }
            }
            System.Diagnostics.Process.Start(AppDir + "\\" + "Reports\\" + DateTime.Now.ToString("yyyy") + "\\" + DateTime.Now.ToString("MM") + "\\");
        }


        /// <summary>
        /// 当前正在拍摄得玻片id
        /// -1 未拍摄
        /// </summary>
        private int CurrSnapSlideIndex = -1;
        private void panel1_Paint(object sender, PaintEventArgs e)
        {
            Graphics graphics = e.Graphics;
            // 背景色
            this.DrawPanelBackground(0,0, this.SlidePanel.Width, _slidePanel_height, 20 , graphics, _whiteBrush);
            
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            
            // 进度条外框
            //graphics.DrawPath(_steelBluePen2, _progress_gp);
            if (this.CurrSnapSlideIndex == -1)
            {
                int slideWidth = _slideWidthSum / 5;
                for (int i = 0; i < 5; i ++)
                {
                    int status = this.addSample.ModelArr[i].Status;
                    if(status >= 1) // 有玻片 未拍摄
                    {
                        this.DrawSnapedSlide(slideWidth * i + _slidePanelPadding + _slideMargin * i, 
                            75, slideWidth, 
                            (int)(slideWidth * 2.5), 4, graphics, _topBrush1, _bottomBrush,i);
                    } 
                    else // 无玻片
                    {
                        this.DrawEmptySlide(slideWidth * i + _slidePanelPadding + _slideMargin * i, 
                            75, slideWidth - 2, 
                            (int)(slideWidth * 2.5) - 2,  4, graphics, _emptySlidePen, i);
                    }
                    
                }
            }
            // 在拍摄 正在拍摄占36% 其它占16%
            else
            {
                int slideWidth = (int)(_slideWidthSum * 0.19);
                int snappingSlideWidth = _slideWidthSum - slideWidth * 4;

                for (int i = 0; i < 5; i++)
                {
                    // 正在拍摄占36% 其它占16%
                    if (this.CurrSnapSlideIndex == i)
                    {
                        this.DrawSnappingSlide((slideWidth + _slideMargin) * i + _slidePanelPadding, 55, snappingSlideWidth, (int)(snappingSlideWidth * 2.5), 8, graphics, _topBrush2, _bottomBrush);
                        continue;
                    }
                    int status = this.addSample.ModelArr[i].Status;
                    int temp_x = this.CurrSnapSlideIndex < i ? (slideWidth * (i - 1) + _slidePanelPadding + _slideMargin * i + snappingSlideWidth) : (slideWidth * i + _slidePanelPadding + _slideMargin * i);
                    if (status == 2) // 有玻片 未计算
                    {
                        this.DrawSnapedSlide(temp_x, 75, slideWidth, (int)(slideWidth * 2.5), 4, graphics, _topBrush1, _bottomBrush, i);
                    }
                    else if (status > 3) // 有玻片 已计算
                    {
                        this.DrawSnapedSlide(temp_x, 75, slideWidth, (int)(slideWidth * 2.5), 4, graphics, _topBrush1, _bottomBrush, i);
                    }
                    else // 无玻片
                    {
                        //this.DrawSnapedSlide(temp_x, 75, slideWidth, (int)(slideWidth * 2.5), 4, graphics, _topBrush1, _bottomBrush, i);
                        this.DrawEmptySlide(temp_x + 1, 75 + 1, slideWidth - 2, (int)(slideWidth * 2.5) - 2 , 4, graphics, _emptySlidePen, i);
                    }

                }

            }
        }



        /// <summary>
        /// 绘制空槽位
        /// </summary>
        private void DrawSnapedSlide(int x, int y, int width, int height, int r, Graphics g, Brush topBrush, Brush bottomBrush, int slide_index)
        {
            int l = 2 * r;
            int rigth_x = x + width;
            int middle_Y = (int)(y + height * 0.75);
            int bottom_y = y + height;
            GraphicsPath topGp = new GraphicsPath();

            // 画上部分
            topGp.AddLine(new Point(x + r, y), new Point(rigth_x - r, y));
            topGp.AddArc(new Rectangle(rigth_x - l, y, l, l), 270F, 90F);
            topGp.AddLine(new Point(rigth_x, y + r), new Point(rigth_x, middle_Y));
            topGp.AddLine(new Point(rigth_x, middle_Y), new Point(x, middle_Y));
            topGp.AddLine(new Point(x, middle_Y - r), new Point(x, y + r));
            topGp.AddArc(new Rectangle(x, y, l, l), 180F, 90F);
            g.FillPath(topBrush, topGp);
            topGp.Dispose();
            // 画下部分
            GraphicsPath bottomGp = new GraphicsPath();
            bottomGp.AddLine(new Point(x, middle_Y), new Point(rigth_x, middle_Y));
            bottomGp.AddLine(new Point(rigth_x, middle_Y), new Point(rigth_x, bottom_y - r));
            bottomGp.AddArc(new Rectangle(rigth_x - l, bottom_y - l, l, l), 0F, 90F);
            bottomGp.AddLine(new Point(rigth_x - r, bottom_y), new Point(x + r, bottom_y));
            bottomGp.AddArc(new Rectangle(x, bottom_y - l, l, l), 90F, 90F);
            bottomGp.AddLine(new Point(x, bottom_y), new Point(x, middle_Y));
            g.FillPath(bottomBrush, bottomGp);
            g.DrawString("#" + (slide_index + 1), _slideTextFont, new SolidBrush(Color.White), (float)(x + width * 0.5 - 13), bottom_y - 30);
            bottomGp.Dispose();
            if (this.addSample.ModelArr[slide_index].Status >= 4)
            {
                g.FillEllipse(_steelBlueBrush, new RectangleF((float)(x + width * 0.5 - 13), y + 17, 26, 26));
                GraphicsPath gp2 = new GraphicsPath();
                gp2.AddLine(new Point((int)(x + width * 0.5 - 9), y + 28), new Point((int)(x + width * 0.5 - 2), y + 35));
                gp2.AddLine(new Point((int)(x + width * 0.5 - 2), y + 35), new Point((int)(x + width * 0.5 + 9), y + 25));
                g.DrawPath(new Pen(Color.White, 4), gp2);
                gp2.Dispose();
            }
        }

        /// <summary>
        /// 绘制正在拍摄的槽位
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="width"></param>
        /// <param name="height"></param>
        /// <param name="r"></param>
        /// <param name="g"></param>
        /// <param name="b"></param>
        private void DrawSnappingSlide(int x, int y, int width, int height, int r, Graphics graphics, Brush topBrush, Brush bottomBrush)
        {
            int l = 2 * r;
            int rigth_x = x + width;
            int middle_Y = (int)(y + height * 0.75);
            int bottom_y = y + height;
            GraphicsPath topGp = new GraphicsPath();

            // 画上部分
            topGp.AddLine(new Point(x + r, y), new Point(rigth_x - r, y));
            topGp.AddArc(new Rectangle(rigth_x - l, y, l, l), 270F, 90F);
            topGp.AddLine(new Point(rigth_x, y + r), new Point(rigth_x, middle_Y));
            topGp.AddLine(new Point(rigth_x, middle_Y), new Point(x, middle_Y));
            topGp.AddLine(new Point(x, middle_Y - r), new Point(x, y + r));
            topGp.AddArc(new Rectangle(x, y, l, l), 180F, 90F);
            graphics.FillPath(topBrush, topGp);
            topGp.Dispose();

            // 画下部分
            GraphicsPath bottomGp = new GraphicsPath();

            bottomGp.AddLine(new Point(x, middle_Y), new Point(rigth_x, middle_Y));
            bottomGp.AddLine(new Point(rigth_x, middle_Y), new Point(rigth_x, bottom_y - r));
            bottomGp.AddArc(new Rectangle(rigth_x - l, bottom_y - l, l, l), 0F, 90F);
            bottomGp.AddLine(new Point(rigth_x - r, bottom_y), new Point(x + r, bottom_y));
            bottomGp.AddArc(new Rectangle(x, bottom_y - l, l, l), 90F, 90F);
            bottomGp.AddLine(new Point(x, bottom_y), new Point(x, middle_Y));
            graphics.FillPath(bottomBrush, bottomGp);
            bottomGp.Dispose();
            graphics.DrawString("#" + (this.CurrSnapSlideIndex + 1), _slideTextFont, new SolidBrush(Color.White), (float)(x + width * 0.5 - 13), bottom_y - 40);
            double step = width / (GlobalProperty.Cols + 1);
            int temp_y = y + height / 4;
            for (int i = 0; i < GlobalProperty.Cols; i++)
            {
                for (int j = 0; j < GlobalProperty.Rows; j++)
                {
                    //graphics.DrawPoint(Color.White, (int)(step * (i + 1) + x), (int)(step * (j) + temp_y), 10);
                    graphics.FillEllipse(Color.White, new RectangleF((float)(step * i + step + x - 5), (float)(step * j + temp_y - 5), 10, 10));
                }
            }
            List<PictureInfo> list = this.addSample.ModelArr[this.CurrSnapSlideIndex].PicInfoList;
            if (list == null || list.Count == 0) return;
            GraphicsPath pathGp = new GraphicsPath();
            for (int i = 0; i < list.Count; i++)
            {
                //graphics.DrawPoint(_steelColor, (int)(step * list[i].X + x + step), (int)(step * list[i].Y + temp_y), 10);
                graphics.FillEllipse(_steelColor, new RectangleF((float)(step * list[i].X + step + x - 5), (float)(step * list[i].Y + temp_y - 5), 10, 10));
                if (i == 0) continue;
                pathGp.AddLine(new PointF((float)(step * list[i - 1].X + x + step), (float)(step * list[i - 1].Y + temp_y)), new PointF((int)(step * list[i].X + x + step), (int)(step * list[i].Y + temp_y)));
                //graphics.DrawLine(_redoPointPen, (float)(step * list[i - 1].Col + x), (float)(step * list[i - 1].Row + temp_y), (int)(step * list[i].Col + x), (int)(step * list[i].Row + temp_y));
            }
            graphics.DrawPath(_steelBluePen3,pathGp);
            pathGp.Dispose();

        }



        private void DrawPanelBackground(int x, int y, int width, int height, int r, Graphics g, Brush b)
        {
            GraphicsPath gp = this.GetRectangleGp(x, y, width, height, r);
            g.FillPath(b, gp);
            gp.Dispose();
        }

        private void DrawEmptySlide(int x, int y, int width, int height, int r, Graphics g, Pen pen, int slide_index)
        {
            GraphicsPath gp = this.GetRectangleGp(x, y, width, height, r);
            g.DrawPath(pen, gp);
            gp.Dispose();
            g.DrawString("#" + (slide_index + 1), _slideTextFont, _emptyTextBrush, (float)(x + width * 0.5 - 12), y + height - 30);
        }

        private GraphicsPath GetRectangleGp(int x, int y, int width, int height, int r)
        {
            int l = 2 * r;
            int rigth_x = x + width;
            int bottom_y = y + height;
            GraphicsPath gp = new GraphicsPath();
            gp.AddLine(new Point(x + r, y), new Point(rigth_x - r, y));
            gp.AddArc(new Rectangle(rigth_x - l, y, l, l), 270F, 90F);
            gp.AddLine(new Point(rigth_x, y + r), new Point(rigth_x, bottom_y - r));
            gp.AddArc(new Rectangle(rigth_x - l, bottom_y - l, l, l), 0F, 90F);
            gp.AddLine(new Point(rigth_x - r, bottom_y), new Point(x + r, bottom_y));
            gp.AddArc(new Rectangle(x, bottom_y - l, l, l), 90F, 90F);
            gp.AddLine(new Point(x, bottom_y - r), new Point(x, y + r));
            gp.AddArc(new Rectangle(x, y, l, l), 180F, 90F);
            return gp;
        }

        /// <summary>
        /// mianPic区域显示状态
        /// 0：视频 1： 图片
        /// </summary>
        private int MainPicStatus;

        private void SetMainPicStatus(int status)
        {
            this.MainPicStatus = status;
        }



        private void PicSetPanel_Load(object sender, EventArgs e)
        {
            /* this.PrePicBtn.Location = new Point(20, 55);
             this.NextPicBtn.Location = new Point(536, 55);*/
            

        }

        internal void KeyDownEvent(object arg1, KeyEventArgs e)
        {
            if ((Control.ModifierKeys & Keys.Control) != 0 && (Control.ModifierKeys & Keys.Alt) != 0 && (Control.ModifierKeys & Keys.Shift) != 0 && e.KeyCode == Keys.H)
            {
                //处理逻辑
                ColorSet color = new ColorSet();
                color.ShowDialog();
            }
        }

        private void MainPicPanel_Load(object sender, EventArgs e)
        {
            //this.MainPicPanel.Height = this.PicSetPanel.Location.Y - 32; 34
            int temp_width = this.MainPicPanel.Width - 34;
            int temp_height = this.MainPicPanel.Height - 34;
            if (temp_width > temp_height * 1920.0 / 1200)
            {
                this.MainPic.Height = temp_height;
                this.MainPic.Width = (int)(temp_height * 1920.0 / 1200);
            }
            else
            {
                this.MainPic.Width = temp_width;
                this.MainPic.Height = (int)(temp_width * 1200.0 / 1920);
            }
            this.MainPic.Location = new Point((this.MainPicPanel.Width - this.MainPic.Width) / 2, (this.MainPicPanel.Height - this.MainPic.Height) / 2);
        }

        private void ProgressBtn_Paint(object sender, PaintEventArgs e)
        {
            Graphics graphics =  e.Graphics;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            if (this.DeviceStatus == 0 || this.DeviceStatus == 1)
            {
                if (this.ProgressBtn_status == 0)
                {
                    // 画外框
                    graphics.DrawPath(this.ProgressBtn_status == 1 ? _activeSteelPen2 : _steelBluePen2, _progress_btn_gp);
                }
                else
                {
                    graphics.FillPath(_steelBlueBrush, _progress_btn_gp);

                }
                StringFormat format = new StringFormat();
                format.Alignment = StringAlignment.Center; //居中 StringAlignment
                format.LineAlignment = StringAlignment.Center;
                string 文本 = "运   行";
                Rectangle 矩形 = new Rectangle(2, 2, _progress_btn_width - 4, _progress_btn_height - 4);
                Font font = new Font("微软雅黑", 11F);
                //Brush 画笔 = Brushes.Blue;
                graphics.DrawString(文本, font, this.ProgressBtn_status == 1 ? _whiteBrush : _steelBlueBrush, 矩形, format);
            }
            else
            {
                int progress_precent = this.SnapPointSize == 0 ? 0 : this.SnapedPointSize * 100 / this.SnapPointSize;
                //progress_precent = tmp;
                int prgress_finish_width = ((int)((this._progress_btn_width - 4) * progress_precent * 0.01));
               
                graphics.FillRectangle(_steelBlueBrush, new RectangleF(2,2, prgress_finish_width, _progress_btn_height - 4));
                Font drawFont = new Font("Arial", 10, System.Drawing.FontStyle.Italic);
                StringFormat format = new StringFormat();
                format.Alignment = StringAlignment.Center; //居中 StringAlignment
                format.LineAlignment = StringAlignment.Center;
                string 文本 = "启  动";
                if (prgress_finish_width < 45)
                {
                    Rectangle 矩形 = new Rectangle(2 + prgress_finish_width, 2, 35, _progress_btn_height - 4);
                    graphics.DrawString(progress_precent + "%", drawFont, _steelBlueBrush, 矩形, format);
                }
                else
                {
                    Rectangle 矩形 = new Rectangle(2, 2, prgress_finish_width + 2 , _progress_btn_height - 4);
                    graphics.DrawString(progress_precent + "%", drawFont, _whiteBrush, 矩形, format);
                }
                graphics.FillPath(_whiteBrush, _progress_btn_left_gp);
                graphics.FillPath(_whiteBrush, _progress_btn_right_gp);
                graphics.DrawPath(_steelBluePen1, _progress_btn_gp);
            }

            
        }

        /// <summary>
        /// 0: 正常 1：鼠标移入
        /// </summary>
        private int ProgressBtn_status;
        /// <summary>
        /// 进度条移入
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ProgressBtn_MouseEnter(object sender, EventArgs e)
        {
            // if (ch)
            if (this.DeviceStatus > 1) return;
            this.ProgressBtn_status = 1;
            this.ProgressBtn.Invalidate();
        }
        /// <summary>
        /// 进度条移出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ProgressBtn_MouseLeave(object sender, EventArgs e)
        {
            if (this.DeviceStatus > 1) return;
            this.ProgressBtn_status = 0;
            this.ProgressBtn.Invalidate();
        }

        /// <summary>
        /// 点击启动
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ProgressBtn_Click(object sender, EventArgs e)
        {
            if (this.DeviceStatus > 1) return;
            if (this.DeviceStatus == 2)
            {
                ShowErrorTip("设备运行中");
                return;
            }
            /* if (this.InitResult != 0)
             {
                 this.InitAdaptor(1);
                 if (this.InitResult != 1) return;
             }*/


            int device_status = 0; // this.Adaptor.AutoDeviceStatus();
            // MessageBox.Show("device_status" + device_status);
            if (device_status != 4)
            {
                if (device_status == 6) ShowErrorTip("仓内样本已扫描完，请出仓重新更换玻片！");
                else if (device_status == 4 || device_status == 3) ShowErrorTip("初始化扫描中！");
                else ShowErrorTip($"设备还未就绪， 状态码：{device_status}");
                return;
            }
            int res = 0; // this.Adaptor.AutoCheckslide();
            if (res == 0)
            {
                // 无破片，空跑一遍
                ShowErrorNotifier("无玻片");
                return;
                //int _temp = this.Adaptor.AutoSlideCapture(this.addSample.ModelArr, this.MainPic);
                /*if (_temp == -1 || _temp == -99)
                {
                    ShowInfoTip("启动失败");
                    return;
                }
                else
                {

                }*/

            }
            if (res <0)
            {
                ShowWarningTip($"错误码：{res}");
                return;
            }
            this.SnapPointSize = 0;
            for (int i = 0; i < 5; i++)
            {
                int slide_status = (res & (int)Math.Pow(2, i)) > 0 ? 1 : 0;
                int sample_status = this.addSample.ModelArr[i].Status;
                if (slide_status == 1 && sample_status > 0 && sample_status < 3)
                {
                    this.SnapPointSize += GlobalProperty.Cols * GlobalProperty.Rows;
                    this.addSample.ModelArr[i].Status = 2;
                    if (!Directory.Exists(AppDir + "//simages//" + DateTime.Now.ToString("yyyyMMdd") + "//" + this.addSample.ModelArr[i].Patient_ID + "_" + this.addSample.ModelArr[i].Test_ID))
                    {
                        Directory.CreateDirectory(AppDir + "//simages//" + DateTime.Now.ToString("yyyyMMdd") + "//" + this.addSample.ModelArr[i].Patient_ID + "_" + this.addSample.ModelArr[i].Test_ID);
                    }
                }
                else if (this.addSample.ModelArr[i].Status == 0 && slide_status == 1)
                {

                    ShowErrorTip($"第{i + 1}个样本未填写信息" + "当前状态："+ this.addSample.ModelArr[i].Status);
                    return;
                }
                else if (this.addSample.ModelArr[i].Status == 1 && slide_status == 0)
                {
                    ShowErrorTip("第" + (i + 1) + "个样本未放置玻片");
                    return;
                }
            }
            //检测读卡器
            /* CardMag cm = new CardMag();
            string info = "";
            int restNumone = 0;
            if (!cm.ReadCard(ref info, ref restNumone))
            {
                ShowErrorDialog("读授权卡失败：" + info);
                mifareone.rf_exit(icdev);
                return;
            }
            if (restNumone == 0)
            {
                ShowErrorDialog("该卡次数为0，无法计算！");
                return;
            }
            // 计算个数
            int calc_num = 0;
            for (int i = 0; i < 5; i++)
            {
                if (this.addSample.ModelArr[i].Status == 1) calc_num++;
            }
            if (calc_num > restNumone)
            {
                ShowErrorNotifier("授权卡次不足，还剩" + restNumone + "次");
                return;
            }*/

            for (int i = 0; i < 5; i++)
            {
                if (this.addSample.ModelArr[i].Status == 1)
                {
                    this.addSample.ModelArr[i].Status = 2;
                }
            }
            this.SlidePanel.Invalidate();
            /*            this.PrePicBtn.Location = new Point(20, 55);
                        this.NextPicBtn.Location = new Point(536, 55);*/
            this.DeviceStatus = 2;



            int temp = 0;// this.Adaptor.AutoSlideCapture(this.addSample.ModelArr, this.MainPic, 2);
            if (temp == -1 || temp == -99)
            {
                ShowInfoTip("启动失败");
                return;
            }
            else
            {

            }
        }

        private void MainPicPanel_Resize(object sender, EventArgs e)
        {
            this.MainPicPanel_Load(null, null);
        }

        private void PagePanel_Click(object sender, EventArgs e)
        {

        }
    }

}


