﻿“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_32\mscorlib\v4.0_4.0.0.0__b77a5c561934e089\mscorlib.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Users\<USER>\source\repos\AIDFI\AIDFI\bin\Debug\AIDFI.exe”，符号已加载。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Users\<USER>\source\repos\AIDFI\AIDFI\bin\Debug\SunnyUI.dll”，符号已加载。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Windows.Forms\v4.0_4.0.0.0__b77a5c561934e089\System.Windows.Forms.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System\v4.0_4.0.0.0__b77a5c561934e089\System.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Drawing\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Drawing.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Users\<USER>\source\repos\AIDFI\AIDFI\bin\Debug\System.Data.SQLite.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_32\System.Data\v4.0_4.0.0.0__b77a5c561934e089\System.Data.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Core\v4.0_4.0.0.0__b77a5c561934e089\System.Core.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_32\System.Transactions\v4.0_4.0.0.0__b77a5c561934e089\System.Transactions.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Xml\v4.0_4.0.0.0__b77a5c561934e089\System.Xml.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Configuration\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.Configuration.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
Native library pre-loader is trying to load native SQLite library "C:\Users\<USER>\source\repos\AIDFI\AIDFI\bin\Debug\x86\SQLite.Interop.dll"...
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_32\System.EnterpriseServices\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.EnterpriseServices.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_32\System.EnterpriseServices\v4.0_4.0.0.0__b03f5f7f11d50a3a\System.EnterpriseServices.Wrapper.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Users\<USER>\source\repos\AIDFI\AIDFI\bin\Debug\GxIAPINET.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\mscorlib.resources\v4.0_4.0.0.0_zh-Hans_b77a5c561934e089\mscorlib.resources.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Windows.Forms.resources\v4.0_4.0.0.0_zh-Hans_b77a5c561934e089\System.Windows.Forms.resources.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Data.resources\v4.0_4.0.0.0_zh-Hans_b77a5c561934e089\System.Data.resources.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Windows\Microsoft.Net\assembly\GAC_MSIL\System.Numerics\v4.0_4.0.0.0__b77a5c561934e089\System.Numerics.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
线程 '<无名称>' (0x1c18) 已退出，返回值为 0 (0x0)。
线程 '<无名称>' (0x4284) 已退出，返回值为 0 (0x0)。
线程 '<无名称>' (0xdac) 已退出，返回值为 0 (0x0)。
线程 '<无名称>' (0x2e90) 已退出，返回值为 0 (0x0)。
线程 '<无名称>' (0x140) 已退出，返回值为 0 (0x0)。
线程 '<无名称>' (0x1f8c) 已退出，返回值为 0 (0x0)。
线程 '<无名称>' (0xbf0) 已退出，返回值为 0 (0x0)。
线程 'T_Showing' (0xe1c) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0xf88) 已退出，返回值为 0 (0x0)。
线程 'T_Showing' (0x35cc) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0x45a4) 已退出，返回值为 0 (0x0)。
“AIDFI.exe”(托管(v4.0.30319)): 已加载“C:\Users\<USER>\source\repos\AIDFI\AIDFI\bin\Debug\NPOI.dll”，已跳过符号加载。已对模块进行了优化并启用了调试器选项“仅我的代码”。
线程 'T_Showing' (0x48f0) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0x4a98) 已退出，返回值为 0 (0x0)。
线程 '<无名称>' (0x4b3c) 已退出，返回值为 0 (0x0)。
线程 'T_Showing' (0x2de0) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0x1d3c) 已退出，返回值为 0 (0x0)。
线程 'T_Showing' (0xac8) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0x700) 已退出，返回值为 0 (0x0)。
线程 'T_Showing' (0x2704) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0x4390) 已退出，返回值为 0 (0x0)。
线程 'T_Showing' (0x4220) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0x3b48) 已退出，返回值为 0 (0x0)。
线程 'T_Showing' (0x3b74) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0x37ac) 已退出，返回值为 0 (0x0)。
线程 '<无名称>' (0x1cfc) 已退出，返回值为 0 (0x0)。
线程 'T_Showing' (0x3a00) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0x4ab8) 已退出，返回值为 0 (0x0)。
线程 'T_Showing' (0x1220) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0x4764) 已退出，返回值为 0 (0x0)。
线程 'T_Showing' (0x19f8) 已退出，返回值为 0 (0x0)。
线程 'T_Floating' (0x440c) 已退出，返回值为 0 (0x0)。
线程 '<无名称>' (0x20b4) 已退出，返回值为 0 (0x0)。
线程 '<无名称>' (0x4b44) 已退出，返回值为 0 (0x0)。
程序“[17036] AIDFI.exe: 托管(v4.0.30319)”已退出，返回值为 0 (0x0)。
