﻿<?xml version="1.0" encoding="utf-8"?><span>
<doc>
  <assembly>
    <name>System.ValueTuple</name>
  </assembly>
  <members>
    <member name="T:System.TupleExtensions">
      <summary>Provides extension methods for tuples to interoperate with language support for tuples in C#.</summary>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``21(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15,``16,``17,``18,``19,``20}}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@,``11@,``12@,``13@,``14@,``15@,``16@,``17@,``18@,``19@,``20@)">
      <summary>Deconstructs a tuple with 21 elements into separate variables.</summary>
      <param name="value">The 21-element tuple to deconstruct into 21 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <param name="item12">The value of the twelfth element, or value.Rest.Item5.</param>
      <param name="item13">The value of the thirteenth element, or value.Rest.Item6.</param>
      <param name="item14">The value of the fourteenth element, or value.Rest.Item7.</param>
      <param name="item15">The value of the fifteenth element, or value.Rest.Rest.Item1.</param>
      <param name="item16">The value of the sixteenth element, or value.Rest.Rest.Item2.</param>
      <param name="item17">The value of the seventeenth element, or value.Rest.Rest.Item3.</param>
      <param name="item18">The value of the eighteenth element, or value.Rest.Rest.Item4.</param>
      <param name="item19">The value of the nineteenth element, or value.Rest.Rest.Item5.</param>
      <param name="item20">The value of the twentieth element, or value.Rest.Rest.Item6.</param>
      <param name="item21">The value of the twenty-first element, or value.Rest.Rest.Item7.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
      <typeparam name="T12">The type of the twelfth element.</typeparam>
      <typeparam name="T13">The type of the thirteenth element.</typeparam>
      <typeparam name="T14">The type of the fourteenth element.</typeparam>
      <typeparam name="T15">The type of the fifteenth element.</typeparam>
      <typeparam name="T16">The type of the sixteenth element.</typeparam>
      <typeparam name="T17">The type of the seventeenth element.</typeparam>
      <typeparam name="T18">The type of the eighteenth element.</typeparam>
      <typeparam name="T19">The type of the nineteenth element.</typeparam>
      <typeparam name="T20">The type of the twentieth element.</typeparam>
      <typeparam name="T21">The type of the twenty-first element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``20(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15,``16,``17,``18,``19}}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@,``11@,``12@,``13@,``14@,``15@,``16@,``17@,``18@,``19@)">
      <summary>Deconstructs a tuple with 20 elements into separate variables.</summary>
      <param name="value">The 20-element tuple to deconstruct into 20 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <param name="item12">The value of the twelfth element, or value.Rest.Item5.</param>
      <param name="item13">The value of the thirteenth element, or value.Rest.Item6.</param>
      <param name="item14">The value of the fourteenth element, or value.Rest.Item7.</param>
      <param name="item15">The value of the fifteenth element, or value.Rest.Rest.Item1.</param>
      <param name="item16">The value of the sixteenth element, or value.Rest.Rest.Item2.</param>
      <param name="item17">The value of the seventeenth element, or value.Rest.Rest.Item3.</param>
      <param name="item18">The value of the eighteenth element, or value.Rest.Rest.Item4.</param>
      <param name="item19">The value of the nineteenth element, or value.Rest.Rest.Item5.</param>
      <param name="item20">The value of the twentieth element, or value.Rest.Rest.Item6.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
      <typeparam name="T12">The type of the twelfth element.</typeparam>
      <typeparam name="T13">The type of the thirteenth element.</typeparam>
      <typeparam name="T14">The type of the fourteenth element.</typeparam>
      <typeparam name="T15">The type of the fifteenth element.</typeparam>
      <typeparam name="T16">The type of the sixteenth element.</typeparam>
      <typeparam name="T17">The type of the seventeenth element.</typeparam>
      <typeparam name="T18">The type of the eighteenth element.</typeparam>
      <typeparam name="T19">The type of the nineteenth element.</typeparam>
      <typeparam name="T20">The type of the twentieth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``19(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15,``16,``17,``18}}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@,``11@,``12@,``13@,``14@,``15@,``16@,``17@,``18@)">
      <summary>Deconstructs a tuple with 19 elements into separate variables.</summary>
      <param name="value">The 19-element tuple to deconstruct into 19 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <param name="item12">The value of the twelfth element, or value.Rest.Item5.</param>
      <param name="item13">The value of the thirteenth element, or value.Rest.Item6.</param>
      <param name="item14">The value of the fourteenth element, or value.Rest.Item7.</param>
      <param name="item15">The value of the fifteenth element, or value.Rest.Rest.Item1.</param>
      <param name="item16">The value of the sixteenth element, or value.Rest.Rest.Item2.</param>
      <param name="item17">The value of the seventeenth element, or value.Rest.Rest.Item3.</param>
      <param name="item18">The value of the eighteenth element, or value.Rest.Rest.Item4.</param>
      <param name="item19">The value of the nineteenth element, or value.Rest.Rest.Item5.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
      <typeparam name="T12">The type of the twelfth element.</typeparam>
      <typeparam name="T13">The type of the thirteenth element.</typeparam>
      <typeparam name="T14">The type of the fourteenth element.</typeparam>
      <typeparam name="T15">The type of the fifteenth element.</typeparam>
      <typeparam name="T16">The type of the sixteenth element.</typeparam>
      <typeparam name="T17">The type of the seventeenth element.</typeparam>
      <typeparam name="T18">The type of the eighteenth element.</typeparam>
      <typeparam name="T19">The type of the nineteenth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``18(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15,``16,``17}}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@,``11@,``12@,``13@,``14@,``15@,``16@,``17@)">
      <summary>Deconstructs a tuple with 18 elements into separate variables.</summary>
      <param name="value">The 18-element tuple to deconstruct into 18 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <param name="item12">The value of the twelfth element, or value.Rest.Item5.</param>
      <param name="item13">The value of the thirteenth element, or value.Rest.Item6.</param>
      <param name="item14">The value of the fourteenth element, or value.Rest.Item7.</param>
      <param name="item15">The value of the fifteenth element, or value.Rest.Rest.Item1.</param>
      <param name="item16">The value of the sixteenth element, or value.Rest.Rest.Item2.</param>
      <param name="item17">The value of the seventeenth element, or value.Rest.Rest.Item3.</param>
      <param name="item18">The value of the eighteenth element, or value.Rest.Rest.Item4.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
      <typeparam name="T12">The type of the twelfth element.</typeparam>
      <typeparam name="T13">The type of the thirteenth element.</typeparam>
      <typeparam name="T14">The type of the fourteenth element.</typeparam>
      <typeparam name="T15">The type of the fifteenth element.</typeparam>
      <typeparam name="T16">The type of the sixteenth element.</typeparam>
      <typeparam name="T17">The type of the seventeenth element.</typeparam>
      <typeparam name="T18">The type of the eighteenth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``17(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15,``16}}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@,``11@,``12@,``13@,``14@,``15@,``16@)">
      <summary>Deconstructs a tuple with 17 elements into separate variables.</summary>
      <param name="value">The 17-element tuple to deconstruct into 17 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <param name="item12">The value of the twelfth element, or value.Rest.Item5.</param>
      <param name="item13">The value of the thirteenth element, or value.Rest.Item6.</param>
      <param name="item14">The value of the fourteenth element, or value.Rest.Item7.</param>
      <param name="item15">The value of the fifteenth element, or value.Rest.Rest.Item1.</param>
      <param name="item16">The value of the sixteenth element, or value.Rest.Rest.Item2.</param>
      <param name="item17">The value of the seventeenth element, or value.Rest.Rest.Item3.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
      <typeparam name="T12">The type of the twelfth element.</typeparam>
      <typeparam name="T13">The type of the thirteenth element.</typeparam>
      <typeparam name="T14">The type of the fourteenth element.</typeparam>
      <typeparam name="T15">The type of the fifteenth element.</typeparam>
      <typeparam name="T16">The type of the sixteenth element.</typeparam>
      <typeparam name="T17">The type of the seventeenth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``16(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15}}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@,``11@,``12@,``13@,``14@,``15@)">
      <summary>Deconstructs a tuple with 16 elements into separate variables.</summary>
      <param name="value">The 16-element tuple to deconstruct into 16 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <param name="item12">The value of the twelfth element, or value.Rest.Item5.</param>
      <param name="item13">The value of the thirteenth element, or value.Rest.Item6.</param>
      <param name="item14">The value of the fourteenth element, or value.Rest.Item7.</param>
      <param name="item15">The value of the fifteenth element, or value.Rest.Rest.Item1.</param>
      <param name="item16">The value of the sixteenth element, or value.Rest.Rest.Item2.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
      <typeparam name="T12">The type of the twelfth element.</typeparam>
      <typeparam name="T13">The type of the thirteenth element.</typeparam>
      <typeparam name="T14">The type of the fourteenth element.</typeparam>
      <typeparam name="T15">The type of the fifteenth element.</typeparam>
      <typeparam name="T16">The type of the sixteenth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``15(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14}}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@,``11@,``12@,``13@,``14@)">
      <summary>Deconstructs a tuple with 15 elements into separate variables.</summary>
      <param name="value">The 15-element tuple to deconstruct into 15 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <param name="item12">The value of the twelfth element, or value.Rest.Item5.</param>
      <param name="item13">The value of the thirteenth element, or value.Rest.Item6.</param>
      <param name="item14">The value of the fourteenth element, or value.Rest.Item7.</param>
      <param name="item15">The value of the fifteenth element, or value.Rest.Rest.Item1.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
      <typeparam name="T12">The type of the twelfth element.</typeparam>
      <typeparam name="T13">The type of the thirteenth element.</typeparam>
      <typeparam name="T14">The type of the fourteenth element.</typeparam>
      <typeparam name="T15">The type of the fifteenth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``14(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@,``11@,``12@,``13@)">
      <summary>Deconstructs a tuple with 14 elements into separate variables.</summary>
      <param name="value">The 14-element tuple to deconstruct into 14 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <param name="item12">The value of the twelfth element, or value.Rest.Item5.</param>
      <param name="item13">The value of the thirteenth element, or value.Rest.Item6.</param>
      <param name="item14">The value of the fourteenth element, or value.Rest.Item7.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
      <typeparam name="T12">The type of the twelfth element.</typeparam>
      <typeparam name="T13">The type of the thirteenth element.</typeparam>
      <typeparam name="T14">The type of the fourteenth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``13(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@,``11@,``12@)">
      <summary>Deconstructs a tuple with 13 elements into separate variables.</summary>
      <param name="value">The 13-element tuple to deconstruct into 13 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <param name="item12">The value of the twelfth element, or value.Rest.Item5.</param>
      <param name="item13">The value of the thirteenth element, or value.Rest.Item6.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
      <typeparam name="T12">The type of the twelfth element.</typeparam>
      <typeparam name="T13">The type of the thirteenth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``12(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@,``11@)">
      <summary>Deconstructs a tuple with 12 elements into separate variables.</summary>
      <param name="value">The 12-element tuple to deconstruct into 12 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <param name="item12">The value of the twelfth element, or value.Rest.Item5.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
      <typeparam name="T12">The type of the twelfth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``11(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@,``10@)">
      <summary>Deconstructs a tuple with 11 elements into separate variables.</summary>
      <param name="value">The 11-element tuple to deconstruct into 11 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <param name="item11">The value of the eleventh element, or value.Rest.Item4.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
      <typeparam name="T11">The type of the eleventh element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``10(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@,``9@)">
      <summary>Deconstructs a tuple with 10 elements into separate variables.</summary>
      <param name="value">The 10-element tuple to deconstruct into 10 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <param name="item10">The value of the tenth element, or value.Rest.Item3.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
      <typeparam name="T10">The type of the tenth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``9(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@,``8@)">
      <summary>Deconstructs a tuple with 9 elements into separate variables.</summary>
      <param name="value">The 9-element tuple to deconstruct into 9 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <param name="item9">The value of the ninth element, or value.Rest.Item2.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
      <typeparam name="T9">The type of the ninth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``8(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7}},``0@,``1@,``2@,``3@,``4@,``5@,``6@,``7@)">
      <summary>Deconstructs a tuple with 8 elements into separate variables.</summary>
      <param name="value">The 8-element tuple to deconstruct into 8 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <param name="item8">The value of the eighth element, or value.Rest.Item1.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``7(System.Tuple{``0,``1,``2,``3,``4,``5,``6},``0@,``1@,``2@,``3@,``4@,``5@,``6@)">
      <summary>Deconstructs a tuple with 7 elements into separate variables.</summary>
      <param name="value">The 7-element tuple to deconstruct into 7 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <param name="item7">The value of the seventh element.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``6(System.Tuple{``0,``1,``2,``3,``4,``5},``0@,``1@,``2@,``3@,``4@,``5@)">
      <summary>Deconstructs a tuple with 6 elements into separate variables.</summary>
      <param name="value">The 6-element tuple to deconstruct into 6 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <param name="item6">The value of the sixth element.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``5(System.Tuple{``0,``1,``2,``3,``4},``0@,``1@,``2@,``3@,``4@)">
      <summary>Deconstructs a tuple with 5 elements into separate variables.</summary>
      <param name="value">The 5-element tuple to deconstruct into 5 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <param name="item5">The value of the fifth element.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``4(System.Tuple{``0,``1,``2,``3},``0@,``1@,``2@,``3@)">
      <summary>Deconstructs a tuple with 4 elements into separate variables.</summary>
      <param name="value">The 4-element tuple to deconstruct into 4 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <param name="item4">The value of the fourth element.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``3(System.Tuple{``0,``1,``2},``0@,``1@,``2@)">
      <summary>Deconstructs a tuple with 3 elements into separate variables.</summary>
      <param name="value">The 3-element tuple to deconstruct into 3 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <param name="item3">The value of the third element.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``2(System.Tuple{``0,``1},``0@,``1@)">
      <summary>Deconstructs a tuple with 2 elements into separate variables.</summary>
      <param name="value">The 2-element tuple to deconstruct into 2 separate variables.</param>
      <param name="item1">The value of the first element.</param>
      <param name="item2">The value of the second element.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.Deconstruct``1(System.Tuple{``0},``0@)">
      <summary>Deconstructs a tuple with 1 element into a separate variable.</summary>
      <param name="value">The 1-element tuple to deconstruct into a separate variable.</param>
      <param name="item1">The value of the single element.</param>
      <typeparam name="T1">The type of the single element.</typeparam>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``21(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10,``11,``12,``13,System.ValueTuple{``14,``15,``16,``17,``18,``19,``20}}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <typeparam name="T17">The type of the seventeenth element., or value.Rest.Rest.Item3.</typeparam>
      <typeparam name="T18">The type of the eighteenth element., or value.Rest.Rest.Item4.</typeparam>
      <typeparam name="T19">The type of the nineteenth element., or value.Rest.Rest.Item5.</typeparam>
      <typeparam name="T20">The type of the twentieth element., or value.Rest.Rest.Item6.</typeparam>
      <typeparam name="T21">The type of the twenty-first element., or value.Rest.Rest.Item7.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``20(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10,``11,``12,``13,System.ValueTuple{``14,``15,``16,``17,``18,``19}}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <typeparam name="T17">The type of the seventeenth element., or value.Rest.Rest.Item3.</typeparam>
      <typeparam name="T18">The type of the eighteenth element., or value.Rest.Rest.Item4.</typeparam>
      <typeparam name="T19">The type of the nineteenth element., or value.Rest.Rest.Item5.</typeparam>
      <typeparam name="T20">The type of the twentieth element., or value.Rest.Rest.Item6.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``19(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10,``11,``12,``13,System.ValueTuple{``14,``15,``16,``17,``18}}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <typeparam name="T17">The type of the seventeenth element., or value.Rest.Rest.Item3.</typeparam>
      <typeparam name="T18">The type of the eighteenth element., or value.Rest.Rest.Item4.</typeparam>
      <typeparam name="T19">The type of the nineteenth element., or value.Rest.Rest.Item5.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``18(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10,``11,``12,``13,System.ValueTuple{``14,``15,``16,``17}}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <typeparam name="T17">The type of the seventeenth element., or value.Rest.Rest.Item3.</typeparam>
      <typeparam name="T18">The type of the eighteenth element., or value.Rest.Rest.Item4.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``17(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10,``11,``12,``13,System.ValueTuple{``14,``15,``16}}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <typeparam name="T17">The type of the seventeenth element., or value.Rest.Rest.Item3.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``16(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10,``11,``12,``13,System.ValueTuple{``14,``15}}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``15(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10,``11,``12,``13,System.ValueTuple{``14}}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``14(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10,``11,``12,``13}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``13(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10,``11,``12}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``12(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10,``11}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``11(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9,``10}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``10(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8,``9}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``9(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7,``8}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``8(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6,System.ValueTuple{``7}})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``7(System.ValueTuple{``0,``1,``2,``3,``4,``5,``6})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``6(System.ValueTuple{``0,``1,``2,``3,``4,``5})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``5(System.ValueTuple{``0,``1,``2,``3,``4})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``4(System.ValueTuple{``0,``1,``2,``3})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``3(System.ValueTuple{``0,``1,``2})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``2(System.ValueTuple{``0,``1})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToTuple``1(System.ValueTuple{``0})">
      <summary>Converts an instance of the ValueTuple structure to an instance of the  Tuple class.</summary>
      <param name="value">The value tuple instance to convert to a tuple.</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <returns>The converted tuple.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``21(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15,``16,``17,``18,``19,``20}}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <typeparam name="T17">The type of the seventeenth element., or value.Rest.Rest.Item3.</typeparam>
      <typeparam name="T18">The type of the eighteenth element., or value.Rest.Rest.Item4.</typeparam>
      <typeparam name="T19">The type of the nineteenth element., or value.Rest.Rest.Item5.</typeparam>
      <typeparam name="T20">The type of the twentieth element., or value.Rest.Rest.Item6.</typeparam>
      <typeparam name="T21">The type of the twenty-first element., or value.Rest.Rest.Item7.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``20(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15,``16,``17,``18,``19}}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <typeparam name="T17">The type of the seventeenth element., or value.Rest.Rest.Item3.</typeparam>
      <typeparam name="T18">The type of the eighteenth element., or value.Rest.Rest.Item4.</typeparam>
      <typeparam name="T19">The type of the nineteenth element., or value.Rest.Rest.Item5.</typeparam>
      <typeparam name="T20">The type of the twentieth element., or value.Rest.Rest.Item6.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``19(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15,``16,``17,``18}}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <typeparam name="T17">The type of the seventeenth element., or value.Rest.Rest.Item3.</typeparam>
      <typeparam name="T18">The type of the eighteenth element., or value.Rest.Rest.Item4.</typeparam>
      <typeparam name="T19">The type of the nineteenth element., or value.Rest.Rest.Item5.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``18(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15,``16,``17}}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <typeparam name="T17">The type of the seventeenth element., or value.Rest.Rest.Item3.</typeparam>
      <typeparam name="T18">The type of the eighteenth element., or value.Rest.Rest.Item4.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``17(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15,``16}}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <typeparam name="T17">The type of the seventeenth element., or value.Rest.Rest.Item3.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``16(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14,``15}}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <typeparam name="T16">The type of the sixteenth element, ., or value.Rest.Rest.Item2.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``15(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13,System.Tuple{``14}}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <typeparam name="T15">The type of the fifteenth element., or value.Rest.Rest.Item1.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``14(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12,``13}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <typeparam name="T14">The type of the fourteenth element, or value.Rest.Item7.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``13(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11,``12}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <typeparam name="T13">The type of the thirteenth element, or value.Rest.Item6.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``12(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10,``11}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <typeparam name="T12">The type of the twelfth element, or value.Rest.Item5.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``11(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9,``10}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <typeparam name="T11">The type of the eleventh element, or value.Rest.Item4.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``10(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8,``9}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <typeparam name="T10">The type of the tenth element, or value.Rest.Item3.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``9(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7,``8}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <typeparam name="T9">The type of the ninth element, or value.Rest.Item2.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``8(System.Tuple{``0,``1,``2,``3,``4,``5,``6,System.Tuple{``7}})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <typeparam name="T8">The type of the eighth element, or value.Rest.Item1.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``7(System.Tuple{``0,``1,``2,``3,``4,``5,``6})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <typeparam name="T7">The type of the seventh element.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``6(System.Tuple{``0,``1,``2,``3,``4,``5})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <typeparam name="T6">The type of the sixth element.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``5(System.Tuple{``0,``1,``2,``3,``4})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <typeparam name="T5">The type of the fifth element.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``4(System.Tuple{``0,``1,``2,``3})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <typeparam name="T4">The type of the fourth element.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``3(System.Tuple{``0,``1,``2})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <typeparam name="T3">The type of the third element.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``2(System.Tuple{``0,``1})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <typeparam name="T2">The type of the second element.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="M:System.TupleExtensions.ToValueTuple``1(System.Tuple{``0})">
      <summary>Converts an instance of the Tuple class to an instance of the  ValueTuple structure.</summary>
      <param name="value">The tuple object to convert to a value tuple</param>
      <typeparam name="T1">The type of the first element.</typeparam>
      <returns>The converted value tuple instance.</returns>
    </member>
    <member name="T:System.ValueTuple`1">
      <summary>Represents a value tuple with a single component.</summary>
      <typeparam name="T1">The type of the value tuple's only element.</typeparam>
    </member>
    <member name="M:System.ValueTuple`1.#ctor(`0)">
      <summary>Initializes a new <see cref="T:System.ValueTuple`1"></see> instance.</summary>
      <param name="item1">The value tuple's first element.</param>
    </member>
    <member name="M:System.ValueTuple`1.CompareTo(System.ValueTuple{`0})">
      <summary>Compares the current <see cref="T:System.ValueTuple`1"></see> instance to a specified <see cref="T:System.ValueTuple`1"></see> instance.</summary>
      <param name="other">The tuple to compare with this instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and                      <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and                                      <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`1.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`1"></see> instance is equal to a specified object.</summary>
      <param name="obj">The object to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`1.Equals(System.ValueTuple{`0})">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`1"></see> instance is equal to a specified <see cref="T:System.ValueTuple`1"></see> instance.</summary>
      <param name="other">The value tuple to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified tuple; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`1.GetHashCode">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`1"></see> instance.</summary>
      <returns>The hash code for the current <see cref="T:System.ValueTuple`1"></see> instance.</returns>
    </member>
    <member name="F:System.ValueTuple`1.Item1">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`1"></see> instance's first element.</summary>
      <returns></returns>
    </member>
    <member name="M:System.ValueTuple`1.ToString">
      <summary>Returns a string that represents the value of this <see cref="T:System.ValueTuple`1"></see> instance.</summary>
      <returns>The string representation of this <see cref="T:System.ValueTuple`1"></see> instance.</returns>
    </member>
    <member name="M:System.ValueTuple`1.System#Collections#IStructuralComparable#CompareTo(System.Object,System.Collections.IComparer)">
      <summary>Compares the current <see cref="T:System.ValueTuple`1"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <param name="comparer">An object that provides custom rules for comparison.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`1.System#Collections#IStructuralEquatable#Equals(System.Object,System.Collections.IEqualityComparer)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`1"></see> instance is equal to a specified object based on a specified comparison method.</summary>
      <param name="other">The object to compare with this instance.</param>
      <param name="comparer">An object that defines the method to use to evaluate whether the two objects are equal.</param>
      <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`1.System#Collections#IStructuralEquatable#GetHashCode(System.Collections.IEqualityComparer)">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`1"></see> instance by using a specified computation method.</summary>
      <param name="comparer">An object whose <see cref="M:System.Collections.IEqualityComparer.GetHashCode(System.Object)"></see> method calculates the hash code of the current <see cref="T:System.ValueTuple`1"></see> instance.</param>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.ValueTuple`1.System#IComparable#CompareTo(System.Object)">
      <summary>Compares the current <see cref="T:System.ValueTuple`1"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">obj</code> in the sort order, as shown in the following table.  </p>
 <table><thead><tr><th> Value  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="T:System.ValueTuple`2">
      <summary>Represents a value tuple with 2 components.</summary>
      <typeparam name="T1">The type of the value tuple's first element.</typeparam>
      <typeparam name="T2">The type of the value tuple's second element.</typeparam>
    </member>
    <member name="M:System.ValueTuple`2.#ctor(`0,`1)">
      <summary>Initializes a new <see cref="T:System.ValueTuple`2"></see> instance.</summary>
      <param name="item1">The value tuple's first element.</param>
      <param name="item2">The value tuple's second element.</param>
    </member>
    <member name="M:System.ValueTuple`2.CompareTo(System.ValueTuple{`0,`1})">
      <summary>Compares the current <see cref="T:System.ValueTuple`2"></see> instance to a specified <see cref="T:System.ValueTuple`2"></see> instance.</summary>
      <param name="other">The tuple to compare with this instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and                      <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and                                      <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`2.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`2"></see> instance is equal to a specified object.</summary>
      <param name="obj">The object to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`2.Equals(System.ValueTuple{`0,`1})">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`2"></see> instance is equal to a specified <see cref="T:System.ValueTuple`2"></see> instance.</summary>
      <param name="other">The value tuple to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified tuple; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`2.GetHashCode">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`2"></see> instance.</summary>
      <returns>The hash code for the current <see cref="T:System.ValueTuple`2"></see> instance.</returns>
    </member>
    <member name="F:System.ValueTuple`2.Item1">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`2"></see> instance's first element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`2.Item2">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`2"></see> instance's second element.</summary>
      <returns></returns>
    </member>
    <member name="M:System.ValueTuple`2.ToString">
      <summary>Returns a string that represents the value of this <see cref="T:System.ValueTuple`2"></see> instance.</summary>
      <returns>The string representation of this <see cref="T:System.ValueTuple`2"></see> instance.</returns>
    </member>
    <member name="M:System.ValueTuple`2.System#Collections#IStructuralComparable#CompareTo(System.Object,System.Collections.IComparer)">
      <summary>Compares the current <see cref="T:System.ValueTuple`2"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <param name="comparer">An object that provides custom rules for comparison.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`2.System#Collections#IStructuralEquatable#Equals(System.Object,System.Collections.IEqualityComparer)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`2"></see> instance is equal to a specified object based on a specified comparison method.</summary>
      <param name="other">The object to compare with this instance.</param>
      <param name="comparer">An object that defines the method to use to evaluate whether the two objects are equal.</param>
      <returns>true if the current instance is equal to the specified objects; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`2.System#Collections#IStructuralEquatable#GetHashCode(System.Collections.IEqualityComparer)">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`2"></see> instance by using a specified computation method.</summary>
      <param name="comparer">An object whose <see cref="M:System.Collections.IEqualityComparer.GetHashCode(System.Object)"></see> method calculates the hash code of the current <see cref="T:System.ValueTuple`2"></see> instance.</param>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.ValueTuple`2.System#IComparable#CompareTo(System.Object)">
      <summary>Compares the current <see cref="T:System.ValueTuple`2"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">obj</code> in the sort order, as shown in the following table.  </p>
 <table><thead><tr><th> Value  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="T:System.ValueTuple`3">
      <summary>Represents a value tuple with 3 components.</summary>
      <typeparam name="T1">The type of the value tuple's first element.</typeparam>
      <typeparam name="T2">The type of the value tuple's second element.</typeparam>
      <typeparam name="T3">The type of the value tuple's third element.</typeparam>
    </member>
    <member name="M:System.ValueTuple`3.#ctor(`0,`1,`2)">
      <summary>Initializes a new <see cref="T:System.ValueTuple`3"></see> instance.</summary>
      <param name="item1">The value tuple's first element.</param>
      <param name="item2">The value tuple's second element.</param>
      <param name="item3">The value tuple's third element.</param>
    </member>
    <member name="M:System.ValueTuple`3.CompareTo(System.ValueTuple{`0,`1,`2})">
      <summary>Compares the current <see cref="T:System.ValueTuple`3"></see> instance to a specified <see cref="T:System.ValueTuple`3"></see> instance.</summary>
      <param name="other">The tuple to compare with this instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and                      <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and                                      <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`3.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`3"></see> instance is equal to a specified object.</summary>
      <param name="obj">The object to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`3.Equals(System.ValueTuple{`0,`1,`2})">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`3"></see> instance is equal to a specified <see cref="T:System.ValueTuple`3"></see> instance.</summary>
      <param name="other">The value tuple to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified tuple; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`3.GetHashCode">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`3"></see> instance.</summary>
      <returns>The hash code for the current <see cref="T:System.ValueTuple`3"></see> instance.</returns>
    </member>
    <member name="F:System.ValueTuple`3.Item1">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`3"></see> instance's first element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`3.Item2">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`3"></see> instance's second element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`3.Item3">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`3"></see> instance's third element.</summary>
      <returns></returns>
    </member>
    <member name="M:System.ValueTuple`3.ToString">
      <summary>Returns a string that represents the value of this <see cref="T:System.ValueTuple`3"></see> instance.</summary>
      <returns>The string representation of this <see cref="T:System.ValueTuple`2"></see> instance.</returns>
    </member>
    <member name="M:System.ValueTuple`3.System#Collections#IStructuralComparable#CompareTo(System.Object,System.Collections.IComparer)">
      <summary>Compares the current <see cref="T:System.ValueTuple`3"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <param name="comparer">An object that provides custom rules for comparison.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`3.System#Collections#IStructuralEquatable#Equals(System.Object,System.Collections.IEqualityComparer)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`3"></see> instance is equal to a specified object based on a specified comparison method.</summary>
      <param name="other">The object to compare with this instance.</param>
      <param name="comparer">An object that defines the method to use to evaluate whether the two objects are equal.</param>
      <returns>true if the current instance is equal to the specified objects; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`3.System#Collections#IStructuralEquatable#GetHashCode(System.Collections.IEqualityComparer)">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`3"></see> instance by using a specified computation method.</summary>
      <param name="comparer">An object whose <see cref="M:System.Collections.IEqualityComparer.GetHashCode(System.Object)"></see> method calculates the hash code of the current <see cref="T:System.ValueTuple`3"></see> instance.</param>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.ValueTuple`3.System#IComparable#CompareTo(System.Object)">
      <summary>Compares the current <see cref="T:System.ValueTuple`3"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">obj</code> in the sort order, as shown in the following table.  </p>
 <table><thead><tr><th> Value  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="T:System.ValueTuple`4">
      <summary>Represents a value tuple with 4 components.</summary>
      <typeparam name="T1">The type of the value tuple's first element.</typeparam>
      <typeparam name="T2">The type of the value tuple's second element.</typeparam>
      <typeparam name="T3">The type of the value tuple's third element.</typeparam>
      <typeparam name="T4">The type of the value tuple's fourth element.</typeparam>
    </member>
    <member name="M:System.ValueTuple`4.#ctor(`0,`1,`2,`3)">
      <summary>Initializes a new <see cref="T:System.ValueTuple`4"></see> instance.</summary>
      <param name="item1">The value tuple's first element.</param>
      <param name="item2">The value tuple's second element.</param>
      <param name="item3">The value tuple's third element.</param>
      <param name="item4">The value tuple's fourth element.</param>
    </member>
    <member name="M:System.ValueTuple`4.CompareTo(System.ValueTuple{`0,`1,`2,`3})">
      <summary>Compares the current <see cref="T:System.ValueTuple`4"></see> instance to a specified <see cref="T:System.ValueTuple`4"></see> instance.</summary>
      <param name="other">The tuple to compare with this instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and                      <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and                                      <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`4.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`4"></see> instance is equal to a specified object.</summary>
      <param name="obj">The object to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`4.Equals(System.ValueTuple{`0,`1,`2,`3})">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`4"></see> instance is equal to a specified <see cref="T:System.ValueTuple`4"></see> instance.</summary>
      <param name="other">The value tuple to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified tuple; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`4.GetHashCode">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`4"></see> instance.</summary>
      <returns>The hash code for the current <see cref="T:System.ValueTuple`4"></see> instance.</returns>
    </member>
    <member name="F:System.ValueTuple`4.Item1">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`4"></see> instance's first element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`4.Item2">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`4"></see> instance's second element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`4.Item3">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`4"></see> instance's third element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`4.Item4">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`4"></see> instance's fourth element.</summary>
      <returns></returns>
    </member>
    <member name="M:System.ValueTuple`4.ToString">
      <summary>Returns a string that represents the value of this <see cref="T:System.ValueTuple`4"></see> instance.</summary>
      <returns>The string representation of this <see cref="T:System.ValueTuple`4"></see> instance.</returns>
    </member>
    <member name="M:System.ValueTuple`4.System#Collections#IStructuralComparable#CompareTo(System.Object,System.Collections.IComparer)">
      <summary>Compares the current <see cref="T:System.ValueTuple`4"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <param name="comparer">An object that provides custom rules for comparison.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`4.System#Collections#IStructuralEquatable#Equals(System.Object,System.Collections.IEqualityComparer)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`4"></see> instance is equal to a specified object based on a specified comparison method.</summary>
      <param name="other">The object to compare with this instance.</param>
      <param name="comparer">An object that defines the method to use to evaluate whether the two objects are equal.</param>
      <returns>true if the current instance is equal to the specified objects; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`4.System#Collections#IStructuralEquatable#GetHashCode(System.Collections.IEqualityComparer)">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`4"></see> instance by using a specified computation method.</summary>
      <param name="comparer">An object whose <see cref="M:System.Collections.IEqualityComparer.GetHashCode(System.Object)"></see> method calculates the hash code of the current <see cref="T:System.ValueTuple`4"></see> instance.</param>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.ValueTuple`4.System#IComparable#CompareTo(System.Object)">
      <summary>Compares the current <see cref="T:System.ValueTuple`4"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">obj</code> in the sort order, as shown in the following table.  </p>
 <table><thead><tr><th> Value  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="T:System.ValueTuple`5">
      <summary>Represents a value tuple with 5 components.</summary>
      <typeparam name="T1">The type of the value tuple's first element.</typeparam>
      <typeparam name="T2">The type of the value tuple's second element.</typeparam>
      <typeparam name="T3">The type of the value tuple's third element.</typeparam>
      <typeparam name="T4">The type of the value tuple's fourth element.</typeparam>
      <typeparam name="T5">The type of the value tuple's fifth element.</typeparam>
    </member>
    <member name="M:System.ValueTuple`5.#ctor(`0,`1,`2,`3,`4)">
      <summary>Initializes a new <see cref="T:System.ValueTuple`5"></see> instance.</summary>
      <param name="item1">The value tuple's first element.</param>
      <param name="item2">The value tuple's second element.</param>
      <param name="item3">The value tuple's third element.</param>
      <param name="item4">The value tuple's fourth element.</param>
      <param name="item5">The value tuple's fifth element.</param>
    </member>
    <member name="M:System.ValueTuple`5.CompareTo(System.ValueTuple{`0,`1,`2,`3,`4})">
      <summary>Compares the current <see cref="T:System.ValueTuple`5"></see> instance to a specified <see cref="T:System.ValueTuple`5"></see> instance.</summary>
      <param name="other">The tuple to compare with this instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and                      <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and                                      <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`5.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`5"></see> instance is equal to a specified object.</summary>
      <param name="obj">The object to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`5.Equals(System.ValueTuple{`0,`1,`2,`3,`4})">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`5"></see> instance is equal to a specified <see cref="T:System.ValueTuple`5"></see> instance.</summary>
      <param name="other">The value tuple to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified tuple; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`5.GetHashCode">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`5"></see> instance.</summary>
      <returns>The hash code for the current <see cref="T:System.ValueTuple`5"></see> instance.</returns>
    </member>
    <member name="F:System.ValueTuple`5.Item1">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`5"></see> instance's first element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`5.Item2">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`5"></see> instance's second element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`5.Item3">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`5"></see> instance's third element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`5.Item4">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`5"></see> instance's fourth element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`5.Item5">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`5"></see> instance's fifth element.</summary>
      <returns></returns>
    </member>
    <member name="M:System.ValueTuple`5.ToString">
      <summary>Returns a string that represents the value of this <see cref="T:System.ValueTuple`5"></see> instance.</summary>
      <returns>The string representation of this <see cref="T:System.ValueTuple`5"></see> instance.</returns>
    </member>
    <member name="M:System.ValueTuple`5.System#Collections#IStructuralComparable#CompareTo(System.Object,System.Collections.IComparer)">
      <summary>Compares the current <see cref="T:System.ValueTuple`5"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <param name="comparer">An object that provides custom rules for comparison.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`5.System#Collections#IStructuralEquatable#Equals(System.Object,System.Collections.IEqualityComparer)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`5"></see> instance is equal to a specified object based on a specified comparison method.</summary>
      <param name="other">The object to compare with this instance.</param>
      <param name="comparer">An object that defines the method to use to evaluate whether the two objects are equal.</param>
      <returns>true if the current instance is equal to the specified objects; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`5.System#Collections#IStructuralEquatable#GetHashCode(System.Collections.IEqualityComparer)">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`5"></see> instance by using a specified computation method.</summary>
      <param name="comparer">An object whose <see cref="M:System.Collections.IEqualityComparer.GetHashCode(System.Object)"></see> method calculates the hash code of the current <see cref="T:System.ValueTuple`5"></see> instance.</param>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.ValueTuple`5.System#IComparable#CompareTo(System.Object)">
      <summary>Compares the current <see cref="T:System.ValueTuple`5"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">obj</code> in the sort order, as shown in the following table.  </p>
 <table><thead><tr><th> Value  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="T:System.ValueTuple`6">
      <summary>Represents a value tuple with 6 components.</summary>
      <typeparam name="T1">The type of the value tuple's first element.</typeparam>
      <typeparam name="T2">The type of the value tuple's second element.</typeparam>
      <typeparam name="T3">The type of the value tuple's third element.</typeparam>
      <typeparam name="T4">The type of the value tuple's fourth element.</typeparam>
      <typeparam name="T5">The type of the value tuple's fifth element.</typeparam>
      <typeparam name="T6">The type of the value tuple's sixth element.</typeparam>
    </member>
    <member name="M:System.ValueTuple`6.#ctor(`0,`1,`2,`3,`4,`5)">
      <summary>Initializes a new <see cref="T:System.ValueTuple`6"></see> instance.</summary>
      <param name="item1">The value tuple's first element.</param>
      <param name="item2">The value tuple's second element.</param>
      <param name="item3">The value tuple's third element.</param>
      <param name="item4">The value tuple's fourth element.</param>
      <param name="item5">The value tuple's fifth element.</param>
      <param name="item6">The value tuple's sixth element.</param>
    </member>
    <member name="M:System.ValueTuple`6.CompareTo(System.ValueTuple{`0,`1,`2,`3,`4,`5})">
      <summary>Compares the current <see cref="T:System.ValueTuple`6"></see> instance to a specified <see cref="T:System.ValueTuple`6"></see> instance.</summary>
      <param name="other">The tuple to compare with this instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and                      <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and                                      <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`6.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`6"></see> instance is equal to a specified object.</summary>
      <param name="obj">The object to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`6.Equals(System.ValueTuple{`0,`1,`2,`3,`4,`5})">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`6"></see> instance is equal to a specified <see cref="T:System.ValueTuple`6"></see> instance.</summary>
      <param name="other">The value tuple to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified tuple; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`6.GetHashCode">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`6"></see> instance.</summary>
      <returns>The hash code for the current <see cref="T:System.ValueTuple`6"></see> instance.</returns>
    </member>
    <member name="F:System.ValueTuple`6.Item1">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`6"></see> instance's first element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`6.Item2">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`6"></see> instance's second element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`6.Item3">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`6"></see> instance's third element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`6.Item4">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`6"></see> instance's fourth element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`6.Item5">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`6"></see> instance's fifth element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`6.Item6">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`6"></see> instance's sixth element.</summary>
      <returns></returns>
    </member>
    <member name="M:System.ValueTuple`6.ToString">
      <summary>Returns a string that represents the value of this <see cref="T:System.ValueTuple`6"></see> instance.</summary>
      <returns>The string representation of this <see cref="T:System.ValueTuple`6"></see> instance.</returns>
    </member>
    <member name="M:System.ValueTuple`6.System#Collections#IStructuralComparable#CompareTo(System.Object,System.Collections.IComparer)">
      <summary>Compares the current <see cref="T:System.ValueTuple`6"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <param name="comparer">An object that provides custom rules for comparison.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`6.System#Collections#IStructuralEquatable#Equals(System.Object,System.Collections.IEqualityComparer)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`6"></see> instance is equal to a specified object based on a specified comparison method.</summary>
      <param name="other">The object to compare with this instance.</param>
      <param name="comparer">An object that defines the method to use to evaluate whether the two objects are equal.</param>
      <returns>true if the current instance is equal to the specified objects; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`6.System#Collections#IStructuralEquatable#GetHashCode(System.Collections.IEqualityComparer)">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`6"></see> instance by using a specified computation method.</summary>
      <param name="comparer">An object whose <see cref="M:System.Collections.IEqualityComparer.GetHashCode(System.Object)"></see> method calculates the hash code of the current <see cref="T:System.ValueTuple`6"></see> instance.</param>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.ValueTuple`6.System#IComparable#CompareTo(System.Object)">
      <summary>Compares the current <see cref="T:System.ValueTuple`6"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">obj</code> in the sort order, as shown in the following table.  </p>
 <table><thead><tr><th> Value  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="T:System.ValueTuple`7">
      <summary>Represents a value tuple with 7 components.</summary>
      <typeparam name="T1">The type of the value tuple's first element.</typeparam>
      <typeparam name="T2">The type of the value tuple's second element.</typeparam>
      <typeparam name="T3">The type of the value tuple's third element.</typeparam>
      <typeparam name="T4">The type of the value tuple's fourth element.</typeparam>
      <typeparam name="T5">The type of the value tuple's fifth element.</typeparam>
      <typeparam name="T6">The type of the value tuple's sixth element.</typeparam>
      <typeparam name="T7">The type of the value tuple's seventh element.</typeparam>
    </member>
    <member name="M:System.ValueTuple`7.#ctor(`0,`1,`2,`3,`4,`5,`6)">
      <summary>Initializes a new <see cref="M:System.ValueTuple`7.#ctor(`0,`1,`2,`3,`4,`5,`6)"></see> instance.</summary>
      <param name="item1">The value tuple's first element.</param>
      <param name="item2">The value tuple's second element.</param>
      <param name="item3">The value tuple's third element.</param>
      <param name="item4">The value tuple's fourth element.</param>
      <param name="item5">The value tuple's fifth element.</param>
      <param name="item6">The value tuple's sixth element.</param>
      <param name="item7">The value tuple's seventh element.</param>
    </member>
    <member name="M:System.ValueTuple`7.CompareTo(System.ValueTuple{`0,`1,`2,`3,`4,`5,`6})">
      <summary>Compares the current <see cref="T:System.ValueTuple`7"></see> instance to a specified <see cref="T:System.ValueTuple`7"></see> instance.</summary>
      <param name="other">The tuple to compare with this instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and                      <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and                                      <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows                                      <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`7.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`7"></see> instance is equal to a specified object.</summary>
      <param name="obj">The object to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`7.Equals(System.ValueTuple{`0,`1,`2,`3,`4,`5,`6})">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`7"></see> instance is equal to a specified <see cref="T:System.ValueTuple`7"></see> instance.</summary>
      <param name="other">The value tuple to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified tuple; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`7.GetHashCode">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`7"></see> instance.</summary>
      <returns>The hash code for the current <see cref="T:System.ValueTuple`7"></see> instance.</returns>
    </member>
    <member name="F:System.ValueTuple`7.Item1">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`7"></see> instance's first element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`7.Item2">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`7"></see> instance's second element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`7.Item3">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`7"></see> instance's third element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`7.Item4">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`7"></see> instance's fourth element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`7.Item5">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`7"></see> instance's fifth element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`7.Item6">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`7"></see> instance's sixth element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`7.Item7">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`7"></see> instance's seventh element.</summary>
      <returns></returns>
    </member>
    <member name="M:System.ValueTuple`7.ToString">
      <summary>Returns a string that represents the value of this <see cref="T:System.ValueTuple`7"></see> instance.</summary>
      <returns>The string representation of this <see cref="T:System.ValueTuple`7"></see> instance.</returns>
    </member>
    <member name="M:System.ValueTuple`7.System#Collections#IStructuralComparable#CompareTo(System.Object,System.Collections.IComparer)">
      <summary>Compares the current <see cref="T:System.ValueTuple`7"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <param name="comparer">An object that provides custom rules for comparison.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`7.System#Collections#IStructuralEquatable#Equals(System.Object,System.Collections.IEqualityComparer)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`7"></see> instance is equal to a specified object based on a specified comparison method.</summary>
      <param name="other">The object to compare with this instance.</param>
      <param name="comparer">An object that defines the method to use to evaluate whether the two objects are equal.</param>
      <returns>true if the current instance is equal to the specified objects; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`7.System#Collections#IStructuralEquatable#GetHashCode(System.Collections.IEqualityComparer)">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`7"></see> instance by using a specified computation method.</summary>
      <param name="comparer">An object whose <see cref="M:System.Collections.IEqualityComparer.GetHashCode(System.Object)"></see> method calculates the hash code of the current <see cref="T:System.ValueTuple`7"></see> instance.</param>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.ValueTuple`7.System#IComparable#CompareTo(System.Object)">
      <summary>Compares the current <see cref="T:System.ValueTuple`7"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">obj</code> in the sort order, as shown in the following table.  </p>
 <table><thead><tr><th> Value  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="T:System.ValueTuple`8">
      <summary>Represents an n-value tuple, where n is 8 or greater.</summary>
      <typeparam name="T1">The type of the value tuple's first element.</typeparam>
      <typeparam name="T2">The type of the value tuple's second element.</typeparam>
      <typeparam name="T3">The type of the value tuple's third element.</typeparam>
      <typeparam name="T4">The type of the value tuple's fourth element.</typeparam>
      <typeparam name="T5">The type of the value tuple's fifth element.</typeparam>
      <typeparam name="T6">The type of the value tuple's sixth element.</typeparam>
      <typeparam name="T7">The type of the value tuple's seventh element.</typeparam>
      <typeparam name="TRest">Any generic value tuple instance that defines the types of the tuple's remaining elements.</typeparam>
    </member>
    <member name="M:System.ValueTuple`8.#ctor(`0,`1,`2,`3,`4,`5,`6,`7)">
      <summary>Initializes a new <see cref="T:System.ValueTuple`8"></see> instance.</summary>
      <param name="item1">The value tuple's first element.</param>
      <param name="item2">The value tuple's second element.</param>
      <param name="item3">The value tuple's third element.</param>
      <param name="item4">The value tuple's fourth element.</param>
      <param name="item5">The value tuple's fifth element.</param>
      <param name="item6">The value tuple's sixth element.</param>
      <param name="item7">The value tuple's seventh element.</param>
      <param name="rest">An instance of any value tuple type that contains the values of the value's tuple's remaining elements.</param>
      <exception cref="T:System.ArgumentException"><paramref name="rest">rest</paramref> is not a generic value tuple type.</exception>
    </member>
    <member name="M:System.ValueTuple`8.CompareTo(System.ValueTuple{`0,`1,`2,`3,`4,`5,`6,`7})">
      <summary>Compares the current <see cref="T:System.ValueTuple`8"></see> instance to a specified <see cref="T:System.ValueTuple`8"></see> instance</summary>
      <param name="other">The tuple to compare with this instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
    </member>
    <member name="M:System.ValueTuple`8.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`8"></see> instance is equal to a specified object.</summary>
      <param name="obj">The object to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`8.Equals(System.ValueTuple{`0,`1,`2,`3,`4,`5,`6,`7})">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`8"></see> instance is equal to a specified <see cref="T:System.ValueTuple`8"></see> instance.</summary>
      <param name="other">The value tuple to compare with this instance.</param>
      <returns>true if the current instance is equal to the specified tuple; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`8.GetHashCode">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`8"></see> instance.</summary>
      <returns>The hash code for the current <see cref="T:System.ValueTuple`8"></see> instance.</returns>
    </member>
    <member name="F:System.ValueTuple`8.Item1">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`8"></see> instance's first element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`8.Item2">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`8"></see> instance's second element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`8.Item3">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`8"></see> instance's third element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`8.Item4">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`8"></see> instance's fourth element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`8.Item5">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`8"></see> instance's fifth element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`8.Item6">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`8"></see> instance's sixth element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`8.Item7">
      <summary>Gets the value of the current <see cref="T:System.ValueTuple`8"></see> instance's seventh element.</summary>
      <returns></returns>
    </member>
    <member name="F:System.ValueTuple`8.Rest">
      <summary>Gets the current <see cref="T:System.ValueTuple`8"></see> instance's remaining elements.</summary>
      <returns></returns>
    </member>
    <member name="M:System.ValueTuple`8.ToString">
      <summary>Returns a string that represents the value of this <see cref="T:System.ValueTuple`8"></see> instance.</summary>
      <returns>The string representation of this <see cref="T:System.ValueTuple`8"></see> instance.</returns>
    </member>
    <member name="M:System.ValueTuple`8.System#Collections#IStructuralComparable#CompareTo(System.Object,System.Collections.IComparer)">
      <summary>Compares the current <see cref="T:System.ValueTuple`8"></see> instance to a specified object by using a specified comparer and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <param name="comparer">An object that provides custom rules for comparison.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">other</code> in the sort order, as shown in the following able.  </p>
 <table><thead><tr><th> Vaue  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
      <exception cref="T:System.ArgumentException"><paramref name="other">other</paramref> is not a <see cref="T:System.ValueTuple`8"></see> object.</exception>
    </member>
    <member name="M:System.ValueTuple`8.System#Collections#IStructuralEquatable#Equals(System.Object,System.Collections.IEqualityComparer)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple`8"></see> instance is equal to a specified object based on a specified comparison method.</summary>
      <param name="other">The object to compare with this instance.</param>
      <param name="comparer">An object that defines the method to use to evaluate whether the two objects are equal.</param>
      <returns>true if the current instance is equal to the specified objects; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple`8.System#Collections#IStructuralEquatable#GetHashCode(System.Collections.IEqualityComparer)">
      <summary>Calculates the hash code for the current <see cref="T:System.ValueTuple`8"></see> instance by using a specified computation method.</summary>
      <param name="comparer">An object whose <see cref="M:System.Collections.IEqualityComparer.GetHashCode(System.Object)"></see> method calculates the hash code of the current <see cref="T:System.ValueTuple`8"></see> instance.</param>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.ValueTuple`8.System#IComparable#CompareTo(System.Object)">
      <summary>Compares the current <see cref="T:System.ValueTuple`8"></see> object to a specified object and returns an integer that indicates whether the current object is before, after, or in the same position as the specified object in the sort order.</summary>
      <param name="other">An object to compare with the current instance.</param>
      <returns><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="2"><p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="1" sourceendlinenumber="1">A signed integer that indicates the relative position of this instance and <code data-dev-comment-type="paramref">obj</code> in the sort order, as shown in the following table.  </p>
 <table><thead><tr><th> Value  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="4" sourceendlinenumber="4"> </th><th> Description  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="6" sourceendlinenumber="6"> </th></tr></thead><tbody><tr><td> A negative integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="8" sourceendlinenumber="8"> </td><td> This instance precedes <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="10" sourceendlinenumber="10"> </td></tr><tr><td> Zero  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="12" sourceendlinenumber="12"> </td><td> This instance and <code data-dev-comment-type="paramref">other</code> have the same position in the sort order.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="14" sourceendlinenumber="14"> </td></tr><tr><td> A positive integer  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="16" sourceendlinenumber="16"> </td><td> This instance follows <code data-dev-comment-type="paramref">other</code>.  <p>
<p sourcefile="System.ValueTuple.yml" sourcestartlinenumber="18" sourceendlinenumber="18"> </td></tr></tbody></table></p>
</returns>
      <exception cref="T:System.ArgumentException"><paramref name="other">other</paramref> is not a <see cref="T:System.ValueTuple`8"></see> object.</exception>
    </member>
    <member name="T:System.ValueTuple">
      <summary>Provides static methods for creating value tuples.</summary>
    </member>
    <member name="M:System.ValueTuple.CompareTo(System.ValueTuple)">
      <summary>Compares the current <see cref="T:System.ValueTuple"></see> instance with a specified object.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <returns>Returns 0 if <paramref name="other">other</paramref> is a <see cref="T:System.ValueTuple"></see> instance and 1 if <paramref name="other">other</paramref> is null.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="other">other</paramref> is not a <see cref="T:System.ValueTuple"></see> instance.</exception>
    </member>
    <member name="M:System.ValueTuple.Create">
      <summary>Creates a new value tuple with zero components.</summary>
      <returns>A new value tuple with no components.</returns>
    </member>
    <member name="M:System.ValueTuple.Create``8(``0,``1,``2,``3,``4,``5,``6,``7)">
      <summary>Creates a new value tuple with 8 components (an octuple).</summary>
      <param name="item1">The value of the value tuple's first component.</param>
      <param name="item2">The value of the value tuple's second component.</param>
      <param name="item3">The value of the value tuple's third component.</param>
      <param name="item4">The value of the value tuple's fourth component.</param>
      <param name="item5">The value of the value tuple's fifth component.</param>
      <param name="item6">The value of the value tuple's sixth component.</param>
      <param name="item7">The value of the value tuple's seventh component.</param>
      <param name="item8">The value of the value tuple's eighth component.</param>
      <typeparam name="T1">The type of the value tuple's first component.</typeparam>
      <typeparam name="T2">The type of the value tuple's second component.</typeparam>
      <typeparam name="T3">The type of the value tuple's third component.</typeparam>
      <typeparam name="T4">The type of the value tuple's fourth component.</typeparam>
      <typeparam name="T5">The type of the value tuple's fifth component.</typeparam>
      <typeparam name="T6">The type of the value tuple's sixth component.</typeparam>
      <typeparam name="T7">The type of the value tuple's seventh component.</typeparam>
      <typeparam name="T8">The type of the value tuple's eighth component.</typeparam>
      <returns>A value tuple with 8 components.</returns>
    </member>
    <member name="M:System.ValueTuple.Create``7(``0,``1,``2,``3,``4,``5,``6)">
      <summary>Creates a new value tuple with 7 components (a septuple).</summary>
      <param name="item1">The value of the value tuple's first component.</param>
      <param name="item2">The value of the value tuple's second component.</param>
      <param name="item3">The value of the value tuple's third component.</param>
      <param name="item4">The value of the value tuple's fourth component.</param>
      <param name="item5">The value of the value tuple's fifth component.</param>
      <param name="item6">The value of the value tuple's sixth component.</param>
      <param name="item7">The value of the value tuple's seventh component.</param>
      <typeparam name="T1">The type of the value tuple's first component.</typeparam>
      <typeparam name="T2">The type of the value tuple's second component.</typeparam>
      <typeparam name="T3">The type of the value tuple's third component.</typeparam>
      <typeparam name="T4">The type of the value tuple's fourth component.</typeparam>
      <typeparam name="T5">The type of the value tuple's fifth component.</typeparam>
      <typeparam name="T6">The type of the value tuple's sixth component.</typeparam>
      <typeparam name="T7">The type of the value tuple's seventh component.</typeparam>
      <returns>A value tuple with 7 components.</returns>
    </member>
    <member name="M:System.ValueTuple.Create``6(``0,``1,``2,``3,``4,``5)">
      <summary>Creates a new value tuple with 6 components (a sexuple).</summary>
      <param name="item1">The value of the value tuple's first component.</param>
      <param name="item2">The value of the value tuple's second component.</param>
      <param name="item3">The value of the value tuple's third component.</param>
      <param name="item4">The value of the value tuple's fourth component.</param>
      <param name="item5">The value of the value tuple's fifth component.</param>
      <param name="item6">The value of the value tuple's sixth component.</param>
      <typeparam name="T1">The type of the value tuple's first component.</typeparam>
      <typeparam name="T2">The type of the value tuple's second component.</typeparam>
      <typeparam name="T3">The type of the value tuple's third component.</typeparam>
      <typeparam name="T4">The type of the value tuple's fourth component.</typeparam>
      <typeparam name="T5">The type of the value tuple's fifth component.</typeparam>
      <typeparam name="T6">The type of the value tuple's sixth component.</typeparam>
      <returns>A value tuple with 6 components.</returns>
    </member>
    <member name="M:System.ValueTuple.Create``5(``0,``1,``2,``3,``4)">
      <summary>Creates a new value tuple with 5 components (a quintuple).</summary>
      <param name="item1">The value of the value tuple's first component.</param>
      <param name="item2">The value of the value tuple's second component.</param>
      <param name="item3">The value of the value tuple's third component.</param>
      <param name="item4">The value of the value tuple's fourth component.</param>
      <param name="item5">The value of the value tuple's fifth component.</param>
      <typeparam name="T1">The type of the value tuple's first component.</typeparam>
      <typeparam name="T2">The type of the value tuple's second component.</typeparam>
      <typeparam name="T3">The type of the value tuple's third component.</typeparam>
      <typeparam name="T4">The type of the value tuple's fourth component.</typeparam>
      <typeparam name="T5">The type of the value tuple's fifth component.</typeparam>
      <returns>A value tuple with 5 components.</returns>
    </member>
    <member name="M:System.ValueTuple.Create``4(``0,``1,``2,``3)">
      <summary>Creates a new value tuple with 4 components (a quadruple).</summary>
      <param name="item1">The value of the value tuple's first component.</param>
      <param name="item2">The value of the value tuple's second component.</param>
      <param name="item3">The value of the value tuple's third component.</param>
      <param name="item4">The value of the value tuple's fourth component.</param>
      <typeparam name="T1">The type of the value tuple's first component.</typeparam>
      <typeparam name="T2">The type of the value tuple's second component.</typeparam>
      <typeparam name="T3">The type of the value tuple's third component.</typeparam>
      <typeparam name="T4">The type of the value tuple's fourth component.</typeparam>
      <returns>A value tuple with 4 components.</returns>
    </member>
    <member name="M:System.ValueTuple.Create``3(``0,``1,``2)">
      <summary>Creates a new value tuple with 3 components (a triple).</summary>
      <param name="item1">The value of the value tuple's first component.</param>
      <param name="item2">The value of the value tuple's second component.</param>
      <param name="item3">The value of the value tuple's third component.</param>
      <typeparam name="T1">The type of the value tuple's first component.</typeparam>
      <typeparam name="T2">The type of the value tuple's second component.</typeparam>
      <typeparam name="T3">The type of the value tuple's third component.</typeparam>
      <returns>A value tuple with 3 components.</returns>
    </member>
    <member name="M:System.ValueTuple.Create``2(``0,``1)">
      <summary>Creates a new value tuple with 2 components (a pair).</summary>
      <param name="item1">The value of the value tuple's first component.</param>
      <param name="item2">The value of the value tuple's second component.</param>
      <typeparam name="T1">The type of the value tuple's first component.</typeparam>
      <typeparam name="T2">The type of the value tuple's second component.</typeparam>
      <returns>A value tuple with 2 components.</returns>
    </member>
    <member name="M:System.ValueTuple.Create``1(``0)">
      <summary>Creates a new value tuple with 1 component (a singleton).</summary>
      <param name="item1">The value of the value tuple's only component.</param>
      <typeparam name="T1">The type of the value tuple's only component.</typeparam>
      <returns>A value tuple with 1 component.</returns>
    </member>
    <member name="M:System.ValueTuple.Equals(System.ValueTuple)">
      <summary>Determines whether two <see cref="T:System.ValueTuple"></see> instances are equal. This method always returns true.</summary>
      <param name="other">The value tuple to compare with the current instance.</param>
      <returns>This method always returns true.</returns>
    </member>
    <member name="M:System.ValueTuple.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple"></see> instance is equal to a specified object.</summary>
      <param name="obj">The object to compare to the current instance.</param>
      <returns>true if <paramref name="obj">obj</paramref> is a <see cref="T:System.ValueTuple"></see> instance; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple.GetHashCode">
      <summary>Returns the hash code for the current <see cref="T:System.ValueTuple"></see> instance.</summary>
      <returns>The hash code for the current <see cref="T:System.ValueTuple"></see> instance.</returns>
    </member>
    <member name="M:System.ValueTuple.ToString">
      <summary>Returns the string representation of this <see cref="T:System.ValueTuple"></see> instance.</summary>
      <returns>This method always returns &quot;()&quot;.</returns>
    </member>
    <member name="M:System.ValueTuple.System#Collections#IStructuralComparable#CompareTo(System.Object,System.Collections.IComparer)">
      <summary>Compares the current <see cref="T:System.ValueTuple"></see> instance to a specified object.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <param name="comparer">An object that provides custom rules for comparison. This parameter is ignored.</param>
      <returns>Returns 0 if <paramref name="other">other</paramref> is a <see cref="T:System.ValueTuple"></see> instance and 1 if <paramref name="other">other</paramref> is null.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="other">other</paramref> is not a <see cref="T:System.ValueTuple"></see> instance.</exception>
    </member>
    <member name="M:System.ValueTuple.System#Collections#IStructuralEquatable#Equals(System.Object,System.Collections.IEqualityComparer)">
      <summary>Returns a value that indicates whether the current <see cref="T:System.ValueTuple"></see> instance is equal to a specified object based on a specified comparison method.</summary>
      <param name="other">The object to compare with this instance.</param>
      <param name="comparer">An object that defines the method to use to evaluate whether the two objects are equal.</param>
      <returns>true if the current instance is equal to the specified object; otherwise, false.</returns>
    </member>
    <member name="M:System.ValueTuple.System#Collections#IStructuralEquatable#GetHashCode(System.Collections.IEqualityComparer)">
      <summary>Returns the hash code for this <see cref="T:System.ValueTuple"></see> instance.</summary>
      <param name="comparer">An object whose <see cref="M:System.Collections.IEqualityComparer.GetHashCode(System.Object)"></see> method computes the hash code. This parameter is ignored.</param>
      <returns>The hash code for this <see cref="T:System.ValueTuple"></see> instance.</returns>
    </member>
    <member name="M:System.ValueTuple.System#IComparable#CompareTo(System.Object)">
      <summary>Compares this <see cref="T:System.ValueTuple"></see> instance with a specified object and returns an indication of their relative values.</summary>
      <param name="other">The object to compare with the current instance</param>
      <returns>0 if <paramref name="other">other</paramref> is a <see cref="T:System.ValueTuple"></see> instance; otherwise, 1 if <paramref name="other">other</paramref> is null.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="other">other</paramref> is not a <see cref="T:System.ValueTuple"></see> instance.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TupleElementNamesAttribute">
      <summary>Indicates that the use of a value tuple on a member is meant to be treated as a tuple with element names.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.TupleElementNamesAttribute.#ctor">
      
    </member>
    <member name="M:System.Runtime.CompilerServices.TupleElementNamesAttribute.#ctor(System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.TupleElementNamesAttribute"></see> class.</summary>
      <param name="transformNames">A string array that specifies, in a pre-order depth-first traversal of a type's construction, which value tuple occurrences are meant to carry element names.</param>
    </member>
    <member name="P:System.Runtime.CompilerServices.TupleElementNamesAttribute.TransformNames">
      <summary>Specifies, in a pre-order depth-first traversal of a type's construction, which value tuple elements are meant to carry element names.</summary>
      <returns>An array that indicates which value tuple elements are meant to carry element names.</returns>
    </member>
  </members>
</doc></span>