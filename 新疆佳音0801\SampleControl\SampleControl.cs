﻿
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace Cellpro



{


    [System.ComponentModel.Designer(typeof(Designer1))]
    public partial class SampleControl : UserControl
    {
        public Action<int> OnEdit;
        public Action<int> OnShowPic;
        public Action<int> OnPrint;
        public Action<int> OnSelectReportPic;

        public int Index { get; set; }
        public SampleControl()
        {
            InitializeComponent();
            this.uiPanel1.RectColor = OrangeColor;
            this.uiToolTip1.SetToolTip(uiSymbolButton4, "选择报告图");
            this.uiToolTip1.SetToolTip(uiSymbolButton2, "打印报告");
            this.uiToolTip1.SetToolTip(uiSymbolButton1, "编辑样本信息");
            this.uiToolTip1.SetToolTip(uiSymbolButton3, "查看图片");
            //this.uiToolTip1.P
        }

        private void uiLabel7_Click(object sender, EventArgs e)
        {

        }

        private void uiLabel3_Click(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// 查看图片
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton3_Click(object sender, EventArgs e)
        {
            if (this.OnShowPic != null)
                this.OnShowPic(this.Index);
        }

        public static readonly Color RedColor = Color.FromArgb(255, 0, 0);
        public static readonly Color LightBlueColor = Color.FromArgb(235, 243, 255);
        public static readonly Color BlueColor = Color.FromArgb(80, 160, 255);
        public static readonly Color GreenColor = Color.FromArgb(0, 255, 0);
        public static readonly Color OrangeColor = Color.FromArgb(220, 155, 40);

        public void SetData(int status, string Patient_ID, string Test_ID, int RightCellNum, int ErrorCellNum, int TotalNum
            , double DFI_Value, string Patient_Name, string Patient_Age, string Sample_type, string Send_Doctor, string Tester, string Send_date, string Test_date
            , string reviewer, string remark )
        {
            
            if (status == 0)
            {
                this.uiToolTip1.RemoveToolTip(this.Label);
                this.Patient_No.Text = "";
                this.Test_No.Text = "";
                this.RightNum.Text = "";
                this.ErrNum.Text = "";
                this.TotalNum.Text = "";
                this.DFIVal.Text = "";
                this.uiPanel1.RectColor = OrangeColor;
            }
            else if (status == 1)
            {
                this.uiToolTip1.RemoveToolTip(this.Label);
                this.Patient_No.Text = Patient_ID;
                this.Test_No.Text = Test_ID;
                this.RightNum.Text = "";
                this.ErrNum.Text = "";
                this.TotalNum.Text = "";
                this.DFIVal.Text = "";
                this.uiPanel1.RectColor = BlueColor;
                this.uiToolTip1.SetToolTip(this.Label, "姓名：" + Patient_Name + "\n年龄：" + Patient_Age + "\n样本类型：" + Sample_type
                    + "\n送检医生" + Send_Doctor + "\n检测人：" + Tester + "\n送检日期：" + Send_date + "\n检测日期：" + Test_date
                    + "\n复核人：" + reviewer + "\n备注" + remark);
            }
            else if (status == 2)
            {
                this.Patient_No.Text = Patient_ID;
                this.Test_No.Text = Test_ID;
                this.RightNum.Text = RightCellNum.ToString();
                this.ErrNum.Text = ErrorCellNum.ToString();
                this.TotalNum.Text = TotalNum.ToString();
                this.DFIVal.Text = DFI_Value.ToString() + "%";
                this.uiPanel1.RectColor = GreenColor;
            }
            if (status >= 1)
            {
                
            }
        }

        /*public void SetData(Dictionary<string, string> dict)
        {
            if (dict["status"] == "0")
            {
                this.Patient_No.Text = "";
                this.Test_No.Text = "";
                this.RightNum.Text = "";
                this.ErrNum.Text = "";
                this.TotalNum.Text = "";
                this.DFIVal.Text = "";
                this.uiPanel1.RectColor = OrangeColor;
            }
            else if (dict["status"] == "1")
            {
                this.Patient_No.Text = dict["Patient_ID"];
                this.Test_No.Text = dict["Test_ID"];
                this.RightNum.Text = "";
                this.ErrNum.Text = "";
                this.TotalNum.Text = "";
                this.DFIVal.Text = "";
                this.uiPanel1.RectColor = BlueColor;
            }
            else if (dict["status"] == "2")
            {
                this.Patient_No.Text = dict["Patient_ID"];
                this.Test_No.Text = Test_ID;
                this.RightNum.Text = RightCellNum.ToString();
                this.ErrNum.Text = ErrorCellNum.ToString();
                this.TotalNum.Text = TotalNum.ToString();
                this.DFIVal.Text = DFI_Value.ToString() + "%";
                this.uiPanel1.RectColor = GreenColor;
            }
        }*/


        private void SampleControl_Load(object sender, EventArgs e)
        {
            this.Label.Text = "#" + (this.Index + 1);
        }

        private void uiLabel10_Click(object sender, EventArgs e)
        {

        }



        /// <summary>
        /// 选择报告图
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton4_Click(object sender, EventArgs e)
        {
            if (this.OnSelectReportPic != null)
                this.OnSelectReportPic(this.Index);
        }

        /// <summary>
        /// 打印报告
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton2_Click(object sender, EventArgs e)
        {
            if (this.OnPrint != null)
            this.OnPrint(this.Index);
        }

        /// <summary>
        /// 编辑
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton1_Click(object sender, EventArgs e)
        {
            if (this.OnEdit != null) OnEdit(this.Index);
        }

        private void SampleControl_Resize(object sender, EventArgs e)
        {
            this.Width = 305;
            this.Height = 126;
        }
    }


    #region 自定义设计类
    /// <summary>
    /// 自定义设计类
    /// </summary>
    public class Designer1 : System.Windows.Forms.Design.ControlDesigner
    {
        /// <summary>
        /// 不允许调整控件的高度
        /// </summary>
        public override SelectionRules SelectionRules
        {
            get
            {
                SelectionRules rules = SelectionRules.Visible | SelectionRules.Moveable;
                    //SelectionRules.LeftSizeable | SelectionRules.RightSizeable | SelectionRules.TopSizeable | SelectionRules.BottomSizeable;

                return rules;
            }
        }
    }
    #endregion

}

