﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{CB1CD4E3-0768-4BDA-982C-49B0C5679ED5}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>SampleControl</RootNamespace>
    <AssemblyName>SampleControl</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Data.Desktop.v21.2, Version=21.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.v21.2, Version=21.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Printing.v21.2.Core, Version=21.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v21.2, Version=21.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="PresentationCore" />
    <Reference Include="SunnyUI, Version=3.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\Cellpro\packages\SunnyUI.3.0.3\lib\net40\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI.Common, Version=3.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\Cellpro\packages\SunnyUI.Common.3.0.3\lib\net40\SunnyUI.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Runtime.Remoting" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="UIAutomationClient" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="PointControl2.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PointControl2.Designer.cs">
      <DependentUpon>PointControl2.cs</DependentUpon>
    </Compile>
    <Compile Include="PointControl1.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PointControl1.Designer.cs">
      <DependentUpon>PointControl1.cs</DependentUpon>
    </Compile>
    <Compile Include="PointControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PointControl.Designer.cs">
      <DependentUpon>PointControl.cs</DependentUpon>
    </Compile>
    <Compile Include="SampleControl4.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SampleControl4.Designer.cs">
      <DependentUpon>SampleControl4.cs</DependentUpon>
    </Compile>
    <Compile Include="SampleControl3.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SampleControl3.Designer.cs">
      <DependentUpon>SampleControl3.cs</DependentUpon>
    </Compile>
    <Compile Include="SlideControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SlideControl.Designer.cs">
      <DependentUpon>SlideControl.cs</DependentUpon>
    </Compile>
    <Compile Include="SlidePanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="SlidePanel.Designer.cs">
      <DependentUpon>SlidePanel.cs</DependentUpon>
    </Compile>
    <Compile Include="ZoomPic.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ZoomPic.Designer.cs">
      <DependentUpon>ZoomPic.cs</DependentUpon>
    </Compile>
    <Compile Include="SampleControl2.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SampleControl2.Designer.cs">
      <DependentUpon>SampleControl2.cs</DependentUpon>
    </Compile>
    <Compile Include="SampleControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SampleControl.Designer.cs">
      <DependentUpon>SampleControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Service Include="{94E38DFF-614B-4cbd-B67C-F211BB35CE8B}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="PointControl2.resx">
      <DependentUpon>PointControl2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PointControl1.resx">
      <DependentUpon>PointControl1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PointControl.resx">
      <DependentUpon>PointControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SampleControl4.resx">
      <DependentUpon>SampleControl4.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SampleControl3.resx">
      <DependentUpon>SampleControl3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SlideControl.resx">
      <DependentUpon>SlideControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZoomPic.resx">
      <DependentUpon>ZoomPic.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SampleControl.resx">
      <DependentUpon>SampleControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SampleControl2.resx">
      <DependentUpon>SampleControl2.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>